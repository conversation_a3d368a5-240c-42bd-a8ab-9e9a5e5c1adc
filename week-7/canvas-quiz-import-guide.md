# Canvas Quiz Import Guide

## Overview
Canvas LMS supports multiple formats for importing quiz questions automatically. Here are the main options and their use cases.

## Import Format Options

### 1. CSV Format (Recommended for Simple Quizzes)

**File:** `quiz-canvas-import.csv` (included in this directory)

**Advantages:**
- Easy to create in Excel or Google Sheets
- Human-readable format
- Quick to edit and modify
- Good for multiple choice questions

**Process:**
1. Create CSV file using the template format
2. Convert CSV to QTI format using online converter
3. Import QTI file into Canvas

**CSV Template Format:**
```csv
Type,Unused,Points,Question,Correct Answer,Option 1,Option 2,Option 3,Option 4
MC,,2,"Question text here",2,"Option A","Option B","Option C","Option D"
```

**Column Definitions:**
- **Type:** MC (Multiple Choice), MR (Multiple Response), TF (True/False)
- **Unused:** Leave blank (required column but not used)
- **Points:** Point value (0-100, can have decimals like 2.5)
- **Question:** The question text
- **Correct Answer:** Number corresponding to correct option (1-4)
- **Option 1-4:** The answer choices

### 2. QTI Format (Industry Standard)

**What is QTI:**
- Question & Test Interoperability standard
- XML-based format
- Supported by most LMS platforms
- More complex but very flexible

**How to Create QTI Files:**
1. **Option A:** Use online converter
   - Upload CSV to: https://canconvert.k-state.edu/qti/
   - Download generated QTI zip file
   
2. **Option B:** Use institutional tools
   - Many universities provide QTI converters
   - Check your institution's Canvas support page

### 3. Canvas Native Import Process

**Steps to Import into Canvas:**

1. **Navigate to Course Settings**
   - Go to your Canvas course
   - Click "Settings" in course navigation
   - Select "Import Course Content"

2. **Choose Import Type**
   - Content Type: "QTI .zip file"
   - Select your converted QTI file
   - Choose destination: "Create New Question Bank"
   - Give question bank a descriptive name

3. **Complete Import**
   - Click "Import"
   - Wait for processing to complete
   - Review imported questions for accuracy

4. **Create Quiz from Question Bank**
   - Go to "Quizzes" in course navigation
   - Create new quiz
   - Add questions from your imported question bank

## Alternative Methods

### Method 1: Direct Text Entry (Canvas Exam Converter)
Some institutions provide text-to-Canvas converters where you can paste formatted text and get QTI output.

**Format Example:**
```
Multiple Choice: Each choice needs to start with a lower case letter
What is CQRS?
a) Command Query Responsibility Segregation *
b) Command Queue Response System
c) Concurrent Query Resource Sharing
d) Command Quality Response Standard
```

### Method 2: AI-Assisted Creation
1. Use AI (ChatGPT, Claude) to generate quiz questions
2. Ask AI to format output as CSV or QTI
3. Import using standard process

### Method 3: Third-Party Tools
- **Respondus:** Professional quiz authoring tool
- **ExamSoft:** Comprehensive assessment platform
- **Google Forms to Canvas:** Various browser extensions

## Best Practices

### Before Import:
- ✅ Test with a small sample first
- ✅ Use consistent formatting
- ✅ Avoid special characters in CSV
- ✅ Keep question text concise
- ✅ Number options consistently

### After Import:
- ✅ Review all imported questions
- ✅ Check answer keys are correct
- ✅ Test quiz functionality
- ✅ Verify point values
- ✅ Preview student view

### Common Issues:
- ❌ Special characters breaking import
- ❌ Incorrect answer key numbering
- ❌ Missing question bank selection
- ❌ Point values not importing correctly

## Troubleshooting

**Import Failed:**
- Check CSV format matches template exactly
- Remove special characters (quotes, commas in text)
- Ensure all required columns are present
- Try smaller batch size

**Questions Look Wrong:**
- Verify correct answer column uses numbers (1-4)
- Check for extra commas in question text
- Ensure consistent option numbering

**Missing Questions:**
- Check if import created new question bank
- Look in "Unfiled Questions" section
- Verify file uploaded completely

## Files Included

1. **quiz.md** - Human-readable markdown format
2. **quiz-canvas-import.csv** - Canvas-compatible CSV format
3. **canvas-quiz-import-guide.md** - This guide

## Next Steps

1. Use the provided CSV file as-is, or modify questions as needed
2. Convert CSV to QTI using the K-State converter link above
3. Import QTI file into your Canvas course
4. Create quiz and add questions from the imported question bank
5. Test the quiz before making it available to students

## Support Resources

- **Canvas Community:** https://community.canvaslms.com/
- **QTI Converter:** https://canconvert.k-state.edu/qti/
- **Canvas Guides:** https://community.canvaslms.com/t5/Canvas-Basics-Guide/tkb-p/basics
