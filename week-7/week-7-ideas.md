# Week 7: Distributed Systems Concepts - Ideas and Topics

## Overview
Building on the foundation from Weeks 1-6 (HTTP/REST, API design, GraphQL, scalability, IaC, and CQRS), Week 7 introduces core distributed systems concepts that are essential for building robust, scalable backend systems.

---

## Topic Ideas for Week 7

### **Option 1: Consensus Algorithms & Distributed Coordination**

#### Core Problem
How do multiple servers agree on a single value or state when they can't trust each other and network failures can occur?

#### Key Concepts
- **CAP Theorem**: Consistency, Availability, Partition Tolerance - pick two
- **Consensus Algorithms**:
  - Raft (easier to understand, widely used)
  - Paxos (theoretical foundation)
- **Leader Election**: How systems choose a coordinator
- **Distributed Locks**: Preventing concurrent access across servers

#### Practical Applications
- Database replication (MongoDB, Cassandra)
- Service discovery (Consul, etcd)
- Distributed task scheduling
- Configuration management

#### Hands-on Demo Ideas
- Implement a simple leader election using Redis
- Simulate network partitions and observe behavior
- Use etcd for distributed configuration

---

### **Option 2: Event-Driven Architecture & Message Queues**

#### Core Problem
How do we build systems where components communicate asynchronously without tight coupling?

#### Key Concepts
- **Message Queues**: RabbitMQ, AWS SQS, Google Pub/Sub
- **Event Streaming**: Kafka, AWS Kinesis
- **Pub/Sub Pattern**: Publishers and subscribers
- **Event Sourcing**: Storing state changes as events
- **Dead Letter Queues**: Handling failed messages
- **Idempotency**: Processing messages exactly once

#### Practical Applications
- Order processing systems
- Notification services
- Real-time analytics pipelines
- Microservices communication

#### Hands-on Demo Ideas
- Build an order processing system with RabbitMQ
- Implement event sourcing for a shopping cart
- Create a notification service using Kafka

---

### **Option 3: Distributed Caching & Data Consistency**

#### Core Problem
How do we keep data consistent across multiple cache servers and databases?

#### Key Concepts
- **Cache Strategies**:
  - Write-through vs Write-back
  - Cache-aside (Lazy Loading)
  - Read-through
- **Cache Invalidation**: The hardest problem in computer science
- **Distributed Caching**: Redis Cluster, Memcached
- **Consistency Models**:
  - Strong consistency
  - Eventual consistency
  - Causal consistency
- **Cache Stampede**: Thundering herd problem

#### Practical Applications
- Session management across servers
- API response caching
- Database query caching
- CDN edge caching

#### Hands-on Demo Ideas
- Implement cache-aside pattern with Redis
- Simulate cache stampede and implement solutions
- Build a distributed session store

---

### **Option 4: Service Mesh & Microservices Communication**

#### Core Problem
How do microservices discover, communicate with, and monitor each other reliably?

#### Key Concepts
- **Service Discovery**: How services find each other
- **Load Balancing**: Client-side vs Server-side
- **Circuit Breakers**: Preventing cascading failures
- **Retry Policies**: Exponential backoff, jitter
- **Service Mesh**: Istio, Linkerd, Consul Connect
- **Observability**: Distributed tracing, metrics, logs

#### Practical Applications
- Microservices architecture
- API gateways
- Fault tolerance
- Traffic management (canary deployments, A/B testing)

#### Hands-on Demo Ideas
- Implement circuit breaker pattern
- Set up service discovery with Consul
- Add distributed tracing with Jaeger/Zipkin

---

### **Option 5: Distributed Transactions & Saga Pattern**

#### Core Problem
How do we maintain data consistency across multiple services/databases without traditional ACID transactions?

#### Key Concepts
- **Two-Phase Commit (2PC)**: Traditional distributed transactions
- **Saga Pattern**: Choreography vs Orchestration
- **Compensating Transactions**: Rolling back distributed operations
- **Eventual Consistency**: Accepting temporary inconsistency
- **Idempotency Keys**: Ensuring operations can be retried safely

#### Practical Applications
- E-commerce order processing
- Payment systems
- Booking systems (hotels, flights)
- Multi-step workflows

#### Hands-on Demo Ideas
- Implement saga pattern for order processing
- Build compensating transactions for failed payments
- Create an orchestration-based saga with a coordinator

---

### **Option 6: Distributed Databases & Sharding**

#### Core Problem
How do we scale databases horizontally across multiple servers?

#### Key Concepts
- **Sharding Strategies**:
  - Hash-based sharding
  - Range-based sharding
  - Geographic sharding
- **Replication**:
  - Master-slave replication
  - Multi-master replication
  - Quorum-based replication
- **Partitioning**: Horizontal vs Vertical
- **Consistent Hashing**: Distributing data evenly
- **Cross-shard Queries**: Challenges and solutions

#### Practical Applications
- Social media platforms (user data)
- E-commerce (product catalogs)
- Analytics systems
- Multi-tenant SaaS applications

#### Hands-on Demo Ideas
- Implement consistent hashing algorithm
- Set up MongoDB sharded cluster
- Simulate rebalancing when adding/removing shards

---

### **Option 7: Rate Limiting & API Throttling**

#### Core Problem
How do we protect our systems from being overwhelmed by too many requests?

#### Key Concepts
- **Rate Limiting Algorithms**:
  - Token bucket
  - Leaky bucket
  - Fixed window
  - Sliding window
- **Distributed Rate Limiting**: Using Redis
- **Backpressure**: Handling overload gracefully
- **API Quotas**: Per-user, per-IP, per-API key
- **DDoS Protection**: Identifying and blocking malicious traffic

#### Practical Applications
- Public APIs (Twitter, GitHub, Stripe)
- Preventing abuse
- Fair resource allocation
- Cost control (cloud services)

#### Hands-on Demo Ideas
- Implement token bucket algorithm with Redis
- Build a rate limiter middleware
- Simulate DDoS and implement protection

---

### **Option 8: Distributed Monitoring & Observability**

#### Core Problem
How do we understand what's happening across hundreds of services and servers?

#### Key Concepts
- **Three Pillars of Observability**:
  - Metrics (Prometheus, Grafana)
  - Logs (ELK stack, Loki)
  - Traces (Jaeger, Zipkin)
- **Distributed Tracing**: Following requests across services
- **Correlation IDs**: Tracking requests end-to-end
- **Alerting**: Proactive problem detection
- **SLIs, SLOs, SLAs**: Measuring reliability

#### Practical Applications
- Debugging production issues
- Performance optimization
- Capacity planning
- Incident response

#### Hands-on Demo Ideas
- Set up Prometheus and Grafana
- Implement distributed tracing
- Create custom metrics and dashboards

---

## 🎯 UPDATED RECOMMENDATIONS (Based on Student Feedback: Prioritize Live Demos & Coding)

### **BEST CHOICE: Distributed Caching & Rate Limiting (Combo of Options 3 & 7)**

**Why This Works Best for Week 7:**
1. ✅ **Extremely hands-on** - Students can see immediate results
2. ✅ **Builds on Week 6** - CQRS read models benefit from caching
3. ✅ **Practical & Job-relevant** - Every backend engineer uses Redis/caching
4. ✅ **Fun to demo** - Can visualize cache hits/misses, stampede effects
5. ✅ **Quick wins** - Students see 10x performance improvements live
6. ✅ **Multiple coding exercises** - Token bucket, cache-aside, distributed locks

**Demo Ideas (All Live-Codeable):**
- **Demo 1:** Cache-aside pattern with Redis (15 min to implement, instant gratification)
- **Demo 2:** Simulate cache stampede and fix it (students love seeing the "before/after")
- **Demo 3:** Token bucket rate limiter (visual counter going up/down)
- **Demo 4:** Distributed session store across multiple servers
- **Demo 5:** Cache invalidation strategies (the famous "hardest problem")

**Student Engagement Hooks:**
- Live dashboard showing cache hit rates
- Load testing to show performance difference (no cache vs with cache)
- Breaking the system intentionally (cache stampede) then fixing it
- Competitive element: "Who can implement the fastest rate limiter?"

---

### **ALTERNATIVE #1: Saga Pattern & Distributed Transactions (Option 5)**

**Why Students Will Love This:**
1. ✅ **Tells a story** - Order processing saga is relatable (e-commerce)
2. ✅ **Visual workflows** - Can draw out the saga steps
3. ✅ **Failure scenarios are fun** - "What if payment fails after inventory is reserved?"
4. ✅ **Connects to Week 6** - CQRS + Saga work together perfectly
5. ✅ **Real-world complexity** - Students see why distributed systems are hard

**Hands-On Demo:**
```
Build an E-commerce Order Saga:
- Service 1: Inventory (reserve items)
- Service 2: Payment (charge card)
- Service 3: Shipping (create shipment)
- Show compensating transactions when payment fails
```

**Why It's Good for Live Coding:**
- Can start with a "broken" system that doesn't handle failures
- Fix it step-by-step with students
- Use a visual dashboard to show saga state
- Simulate failures with a "chaos button"

---

### **ALTERNATIVE #2: Circuit Breakers & Resilience Patterns (Part of Option 4)**

**Why This is Demo-Friendly:**
1. ✅ **Instant visual feedback** - Circuit breaker states (closed/open/half-open)
2. ✅ **Fun to break things** - Students love simulating failures
3. ✅ **Practical library** - Can use Netflix Hystrix/Resilience4j
4. ✅ **Gamification** - "How many failures before circuit opens?"
5. ✅ **Connects to reliability** - SLA/SLO concepts from syllabus

**Live Coding Demo:**
```
Microservice A → Microservice B (flaky)
- Start with no circuit breaker (system hangs)
- Add circuit breaker (system degrades gracefully)
- Add fallback responses
- Add retry with exponential backoff
- Dashboard shows real-time metrics
```

---

### **❌ AVOID FOR WEEK 7 (Based on "Less Theory" Requirement):**

1. **Consensus Algorithms (Option 1)** - Too theoretical, Raft is hard to demo live
2. **Service Mesh (Option 4)** - Setup overhead too high for 2-3 hour session
3. **Distributed Databases (Option 6)** - Already covered in Week 2, sharding is complex
4. **Observability (Option 8)** - Better as a cross-cutting concern throughout course

---

## 🚀 NEW TOPIC SUGGESTIONS (Highly Demo-Friendly)

### **NEW Option 9: WebSockets & Real-Time Communication**

**Why Students Love This:**
- They can build a **live chat app** or **real-time dashboard**
- Immediate visual results (messages appearing in real-time)
- Connects to modern apps (Slack, Discord, trading platforms)
- Fun to demo scaling with Socket.io clusters

**Demo Progression:**
1. Build a simple chat room (30 min)
2. Add presence indicators (who's online)
3. Scale to multiple servers (shows distributed systems challenge)
4. Add Redis for pub/sub across servers
5. Load test with 1000 concurrent users

**Technologies:**
- Java: Spring WebSocket / Socket.IO
- Python: Flask-SocketIO / FastAPI WebSockets

---

### **NEW Option 10: Background Jobs & Task Queues**

**Why This is Practical:**
- Every backend app needs async job processing
- Easy to visualize (job queue, workers, progress bars)
- Real-world use cases students understand (email sending, image processing)
- Can show at-least-once vs exactly-once semantics

**Live Coding Demo:**
```
Email Campaign System:
- User uploads 10,000 email list
- Background worker processes emails
- Dashboard shows progress in real-time
- Handle failures and retries
- Show idempotency in action
```

**Technologies:**
- Java: Spring Batch / Quartz
- Python: Celery / RQ (Redis Queue)

---

### **NEW Option 11: Multi-Tenant Architecture**

**Why This is Relevant:**
- Almost all SaaS products are multi-tenant
- Database per tenant vs shared database with tenant_id
- Data isolation & security concerns
- Performance optimization per tenant

**Hands-On Demo:**
```
Build a SaaS Todo App:
- Tenant A, B, C share one app
- Show data isolation
- Implement tenant-based rate limiting
- Add tenant-specific configurations
- Show how to handle "noisy neighbor" problem
```

---

## 📊 RECOMMENDED WEEK 7 STRUCTURE (Demo-First Approach)

### **Session 1: Distributed Caching (90 minutes)**

**0:00-0:10** - Quick intro to caching (5 slides max)
**0:10-0:30** - DEMO 1: Cache-aside pattern with Redis (LIVE CODE)
**0:30-0:45** - DEMO 2: Cache stampede simulation + fix (LIVE CODE)
**0:45-1:00** - Student Exercise: Implement write-through cache
**1:00-1:15** - DEMO 3: Distributed session store (LIVE CODE)
**1:15-1:30** - Discussion: Cache invalidation strategies (with live examples)

### **Session 2: Rate Limiting & Resilience (90 minutes)**

**0:00-0:10** - Why rate limiting matters (real-world examples)
**0:10-0:30** - DEMO 4: Token bucket algorithm (LIVE CODE with visualization)
**0:30-0:50** - DEMO 5: Circuit breaker pattern (LIVE CODE)
**0:50-1:05** - Student Exercise: Implement sliding window rate limiter
**1:05-1:20** - Load testing demo (show before/after metrics)
**1:20-1:30** - Q&A + Quiz

### **Session 3: Optional Advanced Topics (30 minutes)**

**0:00-0:15** - Distributed locks with Redis (DEMO)
**0:15-0:30** - Cache warming strategies (DEMO)

---

## 💡 INSTRUCTOR TIPS FOR MAXIMUM ENGAGEMENT

1. **Start with a broken system** - Show students the problem first
2. **Live metrics dashboard** - Use Grafana/Prometheus for visual feedback
3. **Chaos button** - Let students click to cause failures
4. **Competitive exercises** - "Who can get the highest cache hit rate?"
5. **Real-world stories** - Share production incidents (Twitter fail whale, etc.)
6. **Keep slides under 10** - Focus on IDE screen time
7. **Have a "follow along" repo** - Students code with you

## 🎓 LEARNING OUTCOMES (Demo-Verified)

By the end of Week 7, students will have:
- ✅ Built 5+ working demos they can show in interviews
- ✅ Implemented caching patterns used at Google/Facebook scale
- ✅ Created production-ready rate limiters
- ✅ Debugged distributed systems issues hands-on
- ✅ Performance-tested their own code
- ✅ Gained confidence in Redis, circuit breakers, and resilience patterns

---

## Suggested Week 7 Structure

### **Module 1: Introduction to Distributed Systems (30 min)**
- What makes a system "distributed"?
- Common challenges: network failures, partial failures, clock synchronization
- CAP theorem overview
- Real-world examples

### **Module 2: Event-Driven Architecture (45 min)**
- Synchronous vs Asynchronous communication
- Message queues vs Event streams
- Pub/Sub pattern
- Use cases and trade-offs

### **Module 3: Hands-on Demo (60 min)**
- Build a notification service with RabbitMQ
- Implement producer and consumer
- Handle failures and retries
- Monitor queue metrics

### **Module 4: Advanced Patterns (30 min)**
- Event sourcing
- CQRS + Event Sourcing (connecting to Week 6)
- Dead letter queues
- Idempotency

### **Module 5: Quiz & Discussion (15 min)**
- Conceptual questions
- Design scenarios
- Trade-off discussions

---

## Additional Resources for Students

### **Reading Materials**
- "Designing Data-Intensive Applications" by Martin Kleppmann (Chapter 5-9)
- AWS Well-Architected Framework (Reliability Pillar)
- Google SRE Book (Distributed Systems chapters)

### **Online Tools**
- RabbitMQ Simulator: https://tryrabbitmq.com/
- Raft Visualization: https://raft.github.io/
- CAP Theorem Interactive: http://cap-theorem.com/

### **Practice Projects**
- Build a distributed chat application
- Implement a job queue system
- Create a real-time analytics dashboard

---

## Notes for Instructor

- **Time allocation**: 2.5-3 hours total
- **Prerequisites**: Students should understand HTTP, REST, databases, and basic scalability concepts
- **Difficulty level**: Intermediate to Advanced
- **Tools needed**: Docker, Python/Java, RabbitMQ or Kafka
- **Assessment**: Combination of quiz and hands-on project

