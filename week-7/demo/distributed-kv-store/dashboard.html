<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Distributed KV Store - Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #d4758c 0%, #6b4423 100%);
            color: #333;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        header {
            background: white;
            padding: 20px 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        h1 {
            color: #8b4058;
            font-size: 2em;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #666;
            font-size: 1em;
        }

        .status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            margin-left: 15px;
        }

        .status.connected {
            background: #10b981;
            color: white;
        }

        .status.disconnected {
            background: #ef4444;
            color: white;
        }

        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .panel {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .panel h2 {
            color: #8b4058;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #8b4058;
            padding-bottom: 10px;
        }

        .feature-toggle {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #f9fafb;
            border-radius: 8px;
            margin-bottom: 15px;
            border-left: 4px solid #8b4058;
        }

        .feature-name {
            font-weight: 600;
            color: #333;
        }

        .feature-description {
            font-size: 0.85em;
            color: #666;
            margin-top: 5px;
        }

        .toggle-btn {
            padding: 8px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
        }

        .toggle-btn.enabled {
            background: #10b981;
            color: white;
        }

        .toggle-btn.disabled {
            background: #e5e7eb;
            color: #666;
        }

        .toggle-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .node-card {
            background: linear-gradient(135deg, #d4758c 0%, #6b4423 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
        }

        .node-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .node-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .node-id {
            font-size: 1.3em;
            font-weight: 700;
        }

        .node-badge {
            padding: 5px 12px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            font-size: 0.85em;
        }

        .node-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 15px;
        }

        .stat {
            background: rgba(255, 255, 255, 0.15);
            padding: 10px;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 1.8em;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .test-panel {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
        }

        .test-panel h2 {
            color: #856404;
            border-bottom-color: #ffc107;
        }

        .test-form {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .test-form input {
            flex: 1;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 1em;
        }

        .test-form button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            background: #8b4058;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .test-form button:hover {
            background: #6b4423;
            transform: translateY(-2px);
        }

        .node-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .node-selector button {
            flex: 1;
            padding: 10px;
            border: 2px solid #8b4058;
            background: #fff5f7;
            color: #8b4058;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .node-selector button.active {
            background: #8b4058;
            color: #fff5f7;
        }

        .result {
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .result.success {
            background: #d1fae5;
            border: 2px solid #10b981;
            color: #065f46;
        }

        .result.error {
            background: #fee2e2;
            border: 2px solid #ef4444;
            color: #991b1b;
        }

        .warning {
            background: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
        }

        .warning-title {
            font-weight: 700;
            color: #92400e;
            margin-bottom: 5px;
        }

        .warning-text {
            color: #78350f;
            font-size: 0.95em;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>
                Distributed Key-Value Store Dashboard
                <span class="status" id="ws-status">Connecting...</span>
            </h1>
            <p class="subtitle">Educational Demo - Distributed Systems Concepts</p>
        </header>

        <div class="grid">
            <!-- Feature Toggles Panel -->
            <div class="panel">
                <h2>Feature Toggles</h2>

                <div class="feature-toggle">
                    <div>
                        <div class="feature-name">Second Node</div>
                        <div class="feature-description">Add a second node (unsynchronized - demonstrates the problem!)</div>
                    </div>
                    <button class="toggle-btn disabled" id="toggle-second-node" onclick="toggleFeature('second_node')">
                        OFF
                    </button>
                </div>

                <div class="feature-toggle">
                    <div>
                        <div class="feature-name">Replication</div>
                        <div class="feature-description">Enable data replication across nodes (Phase 2)</div>
                    </div>
                    <button class="toggle-btn disabled" id="toggle-replication" onclick="toggleFeature('replication')">
                        OFF
                    </button>
                </div>

                <div id="replication-mode-selector" style="display: none; margin-top: 15px; padding: 15px; background: #f0f9ff; border-radius: 6px; border: 2px solid #3b82f6;">
                    <div style="font-weight: 600; margin-bottom: 10px; color: #1e40af;">Replication Mode:</div>
                    <div style="display: flex; gap: 10px;">
                        <button onclick="setReplicationMode('sync')" id="mode-sync" style="flex: 1; padding: 8px; border: 2px solid #3b82f6; background: #3b82f6; color: white; border-radius: 6px; font-weight: 600; cursor: pointer;">
                            SYNC (Wait for ACK)
                        </button>
                        <button onclick="setReplicationMode('async')" id="mode-async" style="flex: 1; padding: 8px; border: 2px solid #3b82f6; background: white; color: #3b82f6; border-radius: 6px; font-weight: 600; cursor: pointer;">
                            ASYNC (Background)
                        </button>
                    </div>
                    <div style="margin-top: 10px; font-size: 0.85em; color: #1e40af;">
                        <div id="mode-description">
                            <strong>SYNC:</strong> Higher latency, strong consistency<br>
                            <strong>ASYNC:</strong> Lower latency, eventual consistency (2s delay)
                        </div>
                    </div>
                </div>

                <div class="feature-toggle">
                    <div>
                        <div class="feature-name">Consensus (Raft)</div>
                        <div class="feature-description">Enable Raft consensus for leader election & automatic failover (Phase 3)</div>
                    </div>
                    <button class="toggle-btn disabled" id="toggle-consensus" onclick="toggleFeature('consensus')">
                        OFF
                    </button>
                </div>

                <div id="raft-info-panel" style="display: none; margin-top: 15px; padding: 15px; background: #f0fdf4; border-radius: 6px; border: 2px solid #10b981;">
                    <div style="font-weight: 600; margin-bottom: 10px; color: #065f46;">Raft Cluster State:</div>
                    <div id="raft-state" style="font-family: monospace; font-size: 0.9em; color: #065f46;">
                        Loading...
                    </div>
                </div>

                <div id="phase1-warning" class="warning" style="display: none;">
                    <div class="warning-title">Problem Demonstrated!</div>
                    <div class="warning-text">
                        Second node is active but NOT synchronized with node-1.
                        Try writing to node-1 and reading from node-2 to see the inconsistency!
                    </div>
                </div>

                <div style="margin-top: 20px; padding-top: 20px; border-top: 2px solid #e5e7eb;">
                    <button onclick="resetCluster()" style="width: 100%; padding: 12px; background: #ef4444; color: white; border: none; border-radius: 6px; font-weight: 600; cursor: pointer; font-size: 1em; transition: all 0.3s;" onmouseover="this.style.background='#dc2626'" onmouseout="this.style.background='#ef4444'">
                        🔄 Reset to Phase 0 (Clear All Data & Features)
                    </button>
                </div>
            </div>

            <!-- Cluster State Panel -->
            <div class="panel">
                <h2>Cluster State</h2>
                <div id="cluster-info">
                    <p style="color: #666;">Connecting to cluster...</p>
                </div>
            </div>
        </div>

        <!-- Nodes Display -->
        <div class="panel">
            <h2>Active Nodes</h2>
            <div id="nodes-container">
                <p style="color: #666;">Loading nodes...</p>
            </div>
        </div>

        <!-- Test Panel -->
        <div class="panel test-panel">
            <h2>Test the System</h2>

            <div class="node-selector">
                <button class="active" data-node="node-1" onclick="selectNode('node-1')">Test on Node 1 (Port 8001)</button>
                <button data-node="node-2" onclick="selectNode('node-2')">Test on Node 2 (Port 8002)</button>
            </div>

            <div class="test-form">
                <input type="text" id="test-key" placeholder="Key (e.g., user:123)" value="user:1">
                <input type="text" id="test-value" placeholder="Value (e.g., Alice)" value="Alice">
                <button onclick="testPut()">PUT</button>
                <button onclick="testGet()">GET</button>
            </div>

            <div id="test-result"></div>
        </div>

        <!-- Replication Log Panel -->
        <div class="panel" id="replication-log-panel" style="display: none;">
            <h2>Replication Log (Sync vs Async)</h2>
            <div id="replication-log" style="font-family: monospace; font-size: 0.9em;">
                <p style="color: #666;">No replication events yet. Enable replication and PUT some data.</p>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let currentNode = 'node-1';
        let currentPort = 8001;
        let featureFlags = {};

        // WebSocket connection
        function connectWebSocket() {
            ws = new WebSocket('ws://localhost:8001/ws');

            ws.onopen = () => {
                console.log('WebSocket connected');
                document.getElementById('ws-status').textContent = 'Connected';
                document.getElementById('ws-status').className = 'status connected';
            };

            ws.onclose = () => {
                console.log('WebSocket disconnected');
                document.getElementById('ws-status').textContent = 'Disconnected';
                document.getElementById('ws-status').className = 'status disconnected';
                setTimeout(connectWebSocket, 3000);
            };

            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);

                if (data.type === 'replication_log') {
                    updateReplicationLog(data.logs);
                } else {
                    updateDashboard(data);
                }
            };

            ws.onerror = (error) => {
                console.error('WebSocket error:', error);
            };
        }

        // Update dashboard with cluster state
        function updateDashboard(clusterState) {
            console.log('Cluster state update:', clusterState);

            // Update feature flags
            featureFlags = clusterState.features;
            updateFeatureToggles(featureFlags);

            // Update cluster info
            const clusterInfo = document.getElementById('cluster-info');
            clusterInfo.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
                    <div class="stat" style="background: #f3f4f6; color: #333;">
                        <div class="stat-value">${clusterState.cluster_size}</div>
                        <div class="stat-label">Active Nodes</div>
                    </div>
                    <div class="stat" style="background: #f3f4f6; color: #333;">
                        <div class="stat-value">${Object.values(featureFlags).filter(v => v === true).length}</div>
                        <div class="stat-label">Enabled Features</div>
                    </div>
                </div>
            `;

            // Update nodes display
            const nodesContainer = document.getElementById('nodes-container');
            nodesContainer.innerHTML = clusterState.nodes.map(node => {
                // Determine node role badge
                let roleBadge = node.is_primary ? 'PRIMARY' : 'SECONDARY';

                // If Raft is enabled, show Raft state
                if (node.raft && node.raft.state) {
                    const raftEmoji = {
                        'leader': '👑',
                        'follower': '👤',
                        'candidate': '🗳️'
                    };
                    roleBadge = `${raftEmoji[node.raft.state]} ${node.raft.state.toUpperCase()}`;
                }

                return `
                <div class="node-card">
                    <div class="node-header">
                        <div class="node-id">${node.id}</div>
                        <div class="node-badge">${roleBadge}</div>
                    </div>
                    ${node.raft && node.raft.state ? `
                        <div style="margin-bottom: 10px; padding: 8px; background: rgba(255,255,255,0.2); border-radius: 6px; font-size: 0.85em;">
                            <strong>Raft:</strong> Term ${node.raft.term} | Log: ${node.raft.log_size} entries
                        </div>
                    ` : ''}
                    <div class="node-stats">
                        <div class="stat">
                            <div class="stat-value">${node.stats.total_keys}</div>
                            <div class="stat-label">Total Keys</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">${node.status === 'active' ? 'Active' : 'Inactive'}</div>
                            <div class="stat-label">Status</div>
                        </div>
                    </div>
                    ${node.stats.sample_data && Object.keys(node.stats.sample_data).length > 0 ? `
                        <div style="margin-top: 15px; padding: 10px; background: rgba(255,255,255,0.15); border-radius: 6px; font-size: 0.9em;">
                            <strong>Sample Data:</strong><br>
                            ${Object.entries(node.stats.sample_data).map(([k, v]) => `${k}: ${v}`).join('<br>')}
                        </div>
                    ` : ''}
                </div>
                `;
            }).join('');

            // Show warning if second node is enabled but not replication
            const warning = document.getElementById('phase1-warning');
            if (featureFlags.second_node_enabled && !featureFlags.replication_enabled) {
                warning.style.display = 'block';
            } else {
                warning.style.display = 'none';
            }
        }

        // Update feature toggle buttons
        function updateFeatureToggles(features) {
            updateToggleButton('toggle-second-node', features.second_node_enabled);
            updateToggleButton('toggle-replication', features.replication_enabled);
            updateToggleButton('toggle-consensus', features.consensus_enabled);

            // Show/hide replication mode selector
            const modeSelector = document.getElementById('replication-mode-selector');
            const logPanel = document.getElementById('replication-log-panel');

            if (features.replication_enabled) {
                modeSelector.style.display = 'block';
                logPanel.style.display = 'block';

                // Update active mode button
                const currentMode = features.replication_mode || 'sync';
                document.getElementById('mode-sync').style.background = currentMode === 'sync' ? '#3b82f6' : 'white';
                document.getElementById('mode-sync').style.color = currentMode === 'sync' ? 'white' : '#3b82f6';
                document.getElementById('mode-async').style.background = currentMode === 'async' ? '#3b82f6' : 'white';
                document.getElementById('mode-async').style.color = currentMode === 'async' ? 'white' : '#3b82f6';
            } else {
                modeSelector.style.display = 'none';
                logPanel.style.display = 'none';
            }

            // Show/hide Raft info panel
            const raftPanel = document.getElementById('raft-info-panel');
            if (features.consensus_enabled) {
                raftPanel.style.display = 'block';
                updateRaftState();  // Fetch Raft state
            } else {
                raftPanel.style.display = 'none';
            }
        }

        function updateToggleButton(btnId, enabled) {
            const btn = document.getElementById(btnId);
            if (enabled) {
                btn.textContent = 'ON';
                btn.className = 'toggle-btn enabled';
            } else {
                btn.textContent = 'OFF';
                btn.className = 'toggle-btn disabled';
            }
        }

        // Toggle feature
        async function toggleFeature(featureName) {
            const currentState = featureFlags[featureName + '_enabled'];
            const newState = !currentState;

            try {
                // Toggle on node-1
                const response = await fetch(`http://localhost:8001/api/features/${featureName}`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({enabled: newState})
                });

                const result = await response.json();
                console.log('Feature toggle result (node-1):', result);

                // If enabling second node or consensus, also toggle on node-2
                if ((featureName === 'second_node' || featureName === 'consensus' || featureName === 'replication') && newState) {
                    try {
                        // First enable second_node on node-2 if needed
                        if (featureName === 'consensus' || featureName === 'replication') {
                            await fetch(`http://localhost:8002/api/features/second_node`, {
                                method: 'POST',
                                headers: {'Content-Type': 'application/json'},
                                body: JSON.stringify({enabled: true})
                            });
                        }

                        // Then enable the requested feature on node-2
                        const response2 = await fetch(`http://localhost:8002/api/features/${featureName}`, {
                            method: 'POST',
                            headers: {'Content-Type': 'application/json'},
                            body: JSON.stringify({enabled: newState})
                        });
                        const result2 = await response2.json();
                        console.log('Feature toggle result (node-2):', result2);
                        showTestResult(`✓ Feature '${featureName}' ${newState ? 'enabled' : 'disabled'} on both nodes`, 'success');
                    } catch (error2) {
                        console.warn('Could not toggle on node-2 (may not be running):', error2);
                        showTestResult(`✓ Feature '${featureName}' ${newState ? 'enabled' : 'disabled'} on node-1. Node-2 may not be running.`, 'success');
                    }
                } else {
                    showTestResult(`✓ Feature '${featureName}' ${newState ? 'enabled' : 'disabled'}`, 'success');
                }

                // If enabling second node, need to start it with docker-compose
                if (featureName === 'second_node' && newState) {
                    showTestResult('Note: If node-2 is not running, start it with: docker-compose --profile with-node-2 up -d', 'error');
                }

            } catch (error) {
                console.error('Error toggling feature:', error);
                showTestResult(`Error: ${error.message}`, 'error');
            }
        }

        // Select node for testing
        function selectNode(nodeId) {
            currentNode = nodeId;
            currentPort = nodeId === 'node-1' ? 8001 : 8002;

            document.querySelectorAll('.node-selector button').forEach(btn => {
                if (btn.dataset.node === nodeId) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            });
        }

        // Test PUT operation
        async function testPut() {
            const key = document.getElementById('test-key').value;
            const value = document.getElementById('test-value').value;

            if (!key || !value) {
                showTestResult('Please enter both key and value', 'error');
                return;
            }

            try {
                const response = await fetch(`http://localhost:${currentPort}/api/kv/${key}`, {
                    method: 'PUT',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({key, value})
                });

                const result = await response.json();
                showTestResult(`PUT Success on ${currentNode}:\n${JSON.stringify(result, null, 2)}`, 'success');
            } catch (error) {
                showTestResult(`Error: ${error.message}`, 'error');
            }
        }

        // Test GET operation
        async function testGet() {
            const key = document.getElementById('test-key').value;

            if (!key) {
                showTestResult('Please enter a key', 'error');
                return;
            }

            try {
                const response = await fetch(`http://localhost:${currentPort}/api/kv/${key}`);

                if (response.ok) {
                    const result = await response.json();
                    showTestResult(`GET Success on ${currentNode}:\n${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    const error = await response.json();
                    showTestResult(`Key not found on ${currentNode}:\n${JSON.stringify(error, null, 2)}`, 'error');
                }
            } catch (error) {
                showTestResult(`Error: ${error.message}`, 'error');
            }
        }

        // Show test result
        function showTestResult(message, type) {
            const resultDiv = document.getElementById('test-result');
            resultDiv.textContent = message;
            resultDiv.className = `result ${type}`;
        }

        // Reset cluster to initial state
        async function resetCluster() {
            if (!confirm('Reset cluster to Phase 0? This will:\n- Clear all data\n- Disable all features\n- Return to monolith state')) {
                return;
            }

            try {
                const response = await fetch('http://localhost:8001/api/reset', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'}
                });

                const result = await response.json();
                console.log('Reset result:', result);

                showTestResult(`✅ Cluster reset successful!\n${JSON.stringify(result, null, 2)}`, 'success');

                // Reload page after short delay to reflect changes
                setTimeout(() => {
                    window.location.reload();
                }, 1500);

            } catch (error) {
                console.error('Error resetting cluster:', error);
                showTestResult(`Error: ${error.message}`, 'error');
            }
        }

        // Set replication mode
        async function setReplicationMode(mode) {
            try {
                const response = await fetch(`http://localhost:8001/api/features/replication_mode?mode=${mode}`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'}
                });

                const result = await response.json();
                console.log('Replication mode set:', result);

                // Update button styles
                document.getElementById('mode-sync').style.background = mode === 'sync' ? '#3b82f6' : 'white';
                document.getElementById('mode-sync').style.color = mode === 'sync' ? 'white' : '#3b82f6';
                document.getElementById('mode-async').style.background = mode === 'async' ? '#3b82f6' : 'white';
                document.getElementById('mode-async').style.color = mode === 'async' ? 'white' : '#3b82f6';

                showTestResult(`Replication mode set to ${mode.toUpperCase()}`, 'success');

            } catch (error) {
                console.error('Error setting replication mode:', error);
                showTestResult(`Error: ${error.message}`, 'error');
            }
        }

        // Update Raft state display
        async function updateRaftState() {
            try {
                const response = await fetch('http://localhost:8001/api/raft/state');
                const data = await response.json();

                if (!data.enabled) {
                    return;
                }

                const stateDiv = document.getElementById('raft-state');

                // State badge colors
                const stateColors = {
                    'leader': '👑 LEADER (green)',
                    'follower': '👤 FOLLOWER (blue)',
                    'candidate': '🗳️ CANDIDATE (yellow)'
                };

                const stateEmoji = {
                    'leader': '👑',
                    'follower': '👤',
                    'candidate': '🗳️'
                };

                stateDiv.innerHTML = `
                    <strong>${stateEmoji[data.state] || '?'} State:</strong> ${data.state.toUpperCase()}<br>
                    <strong>Term:</strong> ${data.term}<br>
                    <strong>Leader:</strong> ${data.leader_id || 'No leader elected'}<br>
                    <strong>Log Size:</strong> ${data.log_size} entries<br>
                    <strong>Committed:</strong> ${data.commit_index + 1} entries
                `;

                // Update again after 2 seconds for real-time updates
                setTimeout(updateRaftState, 2000);

            } catch (error) {
                console.error('Error fetching Raft state:', error);
            }
        }

        // Update replication log display
        function updateReplicationLog(logs) {
            const logContainer = document.getElementById('replication-log');

            if (!logs || logs.length === 0) {
                logContainer.innerHTML = '<p style="color: #666;">No replication events yet. Enable replication and PUT some data.</p>';
                return;
            }

            let html = '<div style="max-height: 400px; overflow-y: auto;">';

            logs.slice().reverse().forEach(log => {
                const modeColor = log.mode === 'SYNC' ? '#3b82f6' : '#f59e0b';
                const statusColor = log.status === 'completed' ? '#10b981' : '#6b7280';

                html += `
                    <div style="margin-bottom: 15px; padding: 12px; background: #f9fafb; border-left: 4px solid ${modeColor}; border-radius: 4px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <div>
                                <span style="background: ${modeColor}; color: white; padding: 2px 8px; border-radius: 4px; font-weight: 600; font-size: 0.85em;">${log.mode}</span>
                                <span style="margin-left: 8px; color: #666;">Key: <strong>${log.key}</strong></span>
                            </div>
                            <span style="color: ${statusColor}; font-weight: 600; font-size: 0.85em;">${log.status}</span>
                        </div>
                        <div style="font-size: 0.8em; color: #666; margin-bottom: 8px;">${log.timestamp}</div>
                `;

                if (log.followers && log.followers.length > 0) {
                    log.followers.forEach(follower => {
                        const icon = follower.status === 'success' ? '✓' : '✗';
                        const color = follower.status === 'success' ? '#10b981' : '#ef4444';
                        const duration = follower.duration_ms !== undefined ? ` (${follower.duration_ms}ms)` : '';

                        html += `
                            <div style="margin-left: 20px; color: ${color};">
                                ${icon} ${follower.node} ${duration}
                            </div>
                        `;
                    });
                }

                html += '</div>';
            });

            html += '</div>';
            logContainer.innerHTML = html;
        }

        // Initialize
        connectWebSocket();
    </script>
</body>
</html>
