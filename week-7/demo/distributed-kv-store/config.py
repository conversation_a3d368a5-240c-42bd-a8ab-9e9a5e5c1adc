"""
==============================================================================
FEATURE FLAGS - Distributed Systems Concepts Control Plane
==============================================================================

Controls which distributed systems features are active in the demo.
Each flag represents a different distributed systems concept.

PROGRESSION:
    Phase 0: Monolith          (no flags enabled)
    Phase 1: Second Node       (second_node_enabled=True)
    Phase 2: Replication       (replication_enabled=True)
    Phase 3: Consensus         (consensus_algorithm="raft")
    Phase 4: Sharding          (sharding_strategy="consistent_hash")
    Phase 5: Multi-tenancy     (multitenancy_enabled=True)

API ENDPOINT: http://localhost:8001/api/features
TOGGLE API:   POST http://localhost:8001/api/features/{feature_name}

==============================================================================
"""

class FeatureFlags:
    """
    Feature flags control plane.

    Each flag can be toggled via HTTP API to demonstrate different
    distributed systems patterns without code changes.
    """

    # PHASE 1: Second Node
    # When enabled: Adds node-2, but data is NOT synchronized
    # Demonstrates: The fundamental distributed systems problem
    second_node_enabled: bool = False

    # PHASE 2: Replication
    # When enabled: Synchronizes data across all nodes
    # Demonstrates: Leader-follower replication pattern
    replication_enabled: bool = False

    # Replication mode: "sync" or "async"
    # sync:  Leader waits for follower ACK before returning (slower, safer)
    # async: Leader returns immediately, replicates in background (faster, eventual consistency)
    replication_mode: str = "sync"  # Options: "sync", "async"

    # Async replication delay (seconds) - simulates network latency
    async_replication_delay: float = 2.0

    # PHASE 3: Consensus
    # When enabled: Implements Raft algorithm for leader election
    # Demonstrates: Fault tolerance and automatic failover
    consensus_enabled: bool = False
    consensus_algorithm: str = "raft"  # Currently only supports "raft"

    # PHASE 4: Sharding
    # When enabled: Partitions data across nodes using hash function
    # Demonstrates: Horizontal scaling and data distribution
    sharding_strategy: str = "none"  # Options: "none", "hash_modulo", "consistent_hash", "range"
    sharding_enabled: bool = False

    # PHASE 5: Multi-tenancy
    # When enabled: Isolates data by namespace
    # Demonstrates: Resource isolation and quota management
    multitenancy_enabled: bool = False

    @classmethod
    def to_dict(cls):
        """Convert feature flags to dictionary for API responses."""
        return {
            "second_node_enabled": cls.second_node_enabled,
            "replication_enabled": cls.replication_enabled,
            "replication_mode": cls.replication_mode,
            "async_replication_delay": cls.async_replication_delay,
            "consensus_enabled": cls.consensus_enabled,
            "consensus_algorithm": cls.consensus_algorithm,
            "sharding_enabled": cls.sharding_enabled,
            "sharding_strategy": cls.sharding_strategy,
            "multitenancy_enabled": cls.multitenancy_enabled,
        }

    @classmethod
    def update(cls, feature_name: str, value):
        """Update a feature flag value."""
        if hasattr(cls, feature_name):
            setattr(cls, feature_name, value)
            return True
        return False

    @classmethod
    def reset(cls):
        """Reset all feature flags to default (Phase 0: Monolith)."""
        cls.second_node_enabled = False
        cls.replication_enabled = False
        cls.replication_mode = "sync"
        cls.async_replication_delay = 2.0
        cls.consensus_enabled = False
        cls.consensus_algorithm = "raft"
        cls.sharding_enabled = False
        cls.sharding_strategy = "none"
        cls.multitenancy_enabled = False
