"""
==============================================================================
RAFT CONSENSUS ALGORITHM - Simplified Educational Implementation
==============================================================================

Implements core Raft consensus for leader election and log replication.
This is a teaching implementation - simplified for clarity, not production-ready.

RAFT STATES:
    ┌──────────┐
    │ FOLLOWER │ ← All nodes start here
    └────┬─────┘
         │ timeout (no heartbeat)
         ▼
    ┌───────────┐
    │ CANDIDATE │ ← Requests votes
    └─────┬─────┘
          │ wins election
          ▼
    ┌────────┐
    │ LEADER │ ← Sends heartbeats, handles writes
    └────────┘

KEY CONCEPTS:
    - Term: Logical clock that increments with each election
    - Log: Ordered sequence of operations (append-only)
    - Quorum: Majority of nodes must agree (N/2 + 1)
    - Heartbeat: Leader sends periodic messages to prevent elections

TEACHING FOCUS:
    - Leader election prevents split-brain
    - Log replication ensures consistency
    - Automatic failover when leader fails

==============================================================================
"""

import asyncio
import logging
import random
from enum import Enum
from typing import Dict, List, Optional, Set
from datetime import datetime
import httpx

logger = logging.getLogger(__name__)


class RaftState(str, Enum):
    """
    Raft node states.

    FOLLOWER:   Normal state, receives heartbeats from leader
    CANDIDATE:  Election in progress, requesting votes
    LEADER:     Won election, handles all writes
    """
    FOLLOWER = "follower"
    CANDIDATE = "candidate"
    LEADER = "leader"


class LogEntry:
    """
    Single entry in the replicated log.

    Each log entry contains:
        - term: When this entry was created
        - command: The operation (PUT/DELETE)
        - key/value: Data for the operation

    EXAMPLE:
        LogEntry(term=1, command="PUT", key="user:1", value="Alice")
    """
    def __init__(self, term: int, command: str, key: str, value: Optional[str] = None):
        self.term = term
        self.command = command  # "PUT" or "DELETE"
        self.key = key
        self.value = value
        self.timestamp = datetime.utcnow().isoformat()

    def to_dict(self) -> dict:
        return {
            "term": self.term,
            "command": self.command,
            "key": self.key,
            "value": self.value,
            "timestamp": self.timestamp,
        }


class RaftNode:
    """
    Simplified Raft consensus implementation for educational purposes.

    ARCHITECTURE:
        Each node runs its own RaftNode instance
        Nodes communicate via HTTP (app.py endpoints)
        Leader election uses randomized timeouts
        Log replication follows append-entries protocol

    SIMPLIFIED ASPECTS (for teaching):
        - Fixed cluster size (2 nodes)
        - No log compaction (snapshots)
        - No configuration changes
        - Simplified timeout mechanism
        - HTTP instead of custom RPC
    """

    def __init__(self, node_id: str, cluster_nodes: List[dict], kv_store=None):
        """
        Initialize a Raft node.

        Args:
            node_id: This node's identifier (e.g., "node-1")
            cluster_nodes: List of all nodes in cluster
                Example: [{"id": "node-1", "url": "http://node-1:8000"},
                         {"id": "node-2", "url": "http://node-2:8000"}]
            kv_store: Reference to the KeyValueStore for applying committed entries
        """
        self.node_id = node_id
        self.cluster_nodes = cluster_nodes
        self.kv_store = kv_store  # Store reference for applying committed entries

        # Raft state
        self.state: RaftState = RaftState.FOLLOWER
        self.current_term: int = 0
        self.voted_for: Optional[str] = None
        self.leader_id: Optional[str] = None

        # Log replication
        self.log: List[LogEntry] = []
        self.commit_index: int = -1  # Highest log entry committed
        self.last_applied: int = -1  # Highest log entry applied to state machine

        # Leader-specific state (used only when leader)
        self.next_index: Dict[str, int] = {}  # For each follower, next log entry to send
        self.match_index: Dict[str, int] = {}  # For each follower, highest replicated entry

        # Timing
        self.last_heartbeat_time = asyncio.get_event_loop().time()
        self.election_timeout = self._random_election_timeout()

        # Background tasks
        self.heartbeat_task: Optional[asyncio.Task] = None
        self.election_task: Optional[asyncio.Task] = None

        logger.info(f"[{self.node_id}] RaftNode initialized as FOLLOWER, term=0")

    def _random_election_timeout(self) -> float:
        """
        Generate random election timeout between 150-300ms.

        WHY RANDOM?
            Prevents multiple nodes from timing out simultaneously
            Reduces split votes in elections

        TEACHING NOTE:
            In production: 150-300ms is typical
            For demo: Can increase to 3-5s for visibility
        """
        return random.uniform(3.0, 5.0)  # Seconds (increased for demo visibility)

    def _get_other_nodes(self) -> List[dict]:
        """Get all cluster nodes except this one."""
        return [n for n in self.cluster_nodes if n["id"] != self.node_id]

    def _get_quorum_size(self) -> int:
        """
        Calculate quorum size (majority).

        FORMULA: floor(N/2) + 1

        EXAMPLES:
            2 nodes → quorum = 2 (both must agree)
            3 nodes → quorum = 2 (majority)
            5 nodes → quorum = 3 (majority)

        TEACHING NOTE:
            Quorum prevents split-brain scenarios
            Ensures only one leader can be elected per term
        """
        return len(self.cluster_nodes) // 2 + 1

    async def start(self):
        """
        Start the Raft node background processes.

        STARTS:
            - Election timeout monitor (all states)
            - Heartbeat sender (leader only)
        """
        logger.info(f"[{self.node_id}] Starting Raft consensus")
        self.election_task = asyncio.create_task(self._election_timeout_monitor())

    async def stop(self):
        """Stop all background tasks."""
        if self.election_task:
            self.election_task.cancel()
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
        logger.info(f"[{self.node_id}] Raft consensus stopped")

    async def _election_timeout_monitor(self):
        """
        Monitor for election timeout.

        BEHAVIOR:
            - If FOLLOWER: Start election if no heartbeat received
            - If CANDIDATE: Restart election if timeout expires
            - If LEADER: No timeout (sends heartbeats instead)

        TIMING:
            Checks every 0.5 seconds
            Timeout: 3-5 seconds (randomized)
        """
        while True:
            await asyncio.sleep(0.5)  # Check twice per second

            if self.state == RaftState.LEADER:
                continue  # Leaders don't timeout

            time_since_heartbeat = asyncio.get_event_loop().time() - self.last_heartbeat_time

            if time_since_heartbeat > self.election_timeout:
                logger.info(f"[{self.node_id}] Election timeout! No heartbeat for {time_since_heartbeat:.1f}s")
                await self._start_election()

    async def _start_election(self):
        """
        Start leader election.

        RAFT ELECTION ALGORITHM:
            1. Increment term
            2. Transition to CANDIDATE
            3. Vote for self
            4. Send RequestVote RPC to all nodes
            5. If majority votes → become LEADER
            6. If another leader found → become FOLLOWER
            7. If timeout → restart election

        TEACHING MOMENT:
            Show students how randomized timeouts prevent ties
            Demonstrate term-based voting (can't vote twice in same term)
        """
        self.current_term += 1
        self.state = RaftState.CANDIDATE
        self.voted_for = self.node_id  # Vote for self
        self.leader_id = None

        logger.info(f"[{self.node_id}] 🗳️  Starting election for term {self.current_term}")

        votes_received = 1  # Count self vote
        other_nodes = self._get_other_nodes()

        # Request votes from all other nodes
        async with httpx.AsyncClient(timeout=2.0) as client:
            for node in other_nodes:
                try:
                    response = await client.post(
                        f"{node['url']}/api/raft/request_vote",
                        json={
                            "term": self.current_term,
                            "candidate_id": self.node_id,
                            "last_log_index": len(self.log) - 1,
                            "last_log_term": self.log[-1].term if self.log else 0,
                        }
                    )

                    if response.status_code == 200:
                        data = response.json()

                        # Check if we received a vote
                        if data.get("vote_granted"):
                            votes_received += 1
                            logger.info(f"[{self.node_id}] ✓ Received vote from {node['id']} (total: {votes_received})")

                        # If other node has higher term, step down
                        if data.get("term", 0) > self.current_term:
                            logger.info(f"[{self.node_id}] Found higher term {data['term']}, stepping down")
                            await self._step_down(data["term"])
                            return

                except Exception as e:
                    logger.warning(f"[{self.node_id}] Failed to get vote from {node['id']}: {e}")

        # Check if we won the election
        quorum = self._get_quorum_size()
        if votes_received >= quorum:
            logger.info(f"[{self.node_id}] 👑 WON ELECTION with {votes_received}/{len(self.cluster_nodes)} votes (quorum={quorum})")
            await self._become_leader()
        else:
            logger.info(f"[{self.node_id}] Lost election with {votes_received}/{len(self.cluster_nodes)} votes (needed {quorum})")
            # Reset timeout and try again
            self.election_timeout = self._random_election_timeout()
            self.last_heartbeat_time = asyncio.get_event_loop().time()

    async def _become_leader(self):
        """
        Transition to LEADER state.

        LEADER RESPONSIBILITIES:
            1. Send periodic heartbeats to all followers
            2. Handle all write requests (PUT/DELETE)
            3. Replicate log entries to followers
            4. Commit entries once replicated to majority

        INITIALIZATION:
            - Set next_index for each follower to end of log
            - Set match_index to -1 (no entries replicated yet)
            - Start heartbeat task
        """
        self.state = RaftState.LEADER
        self.leader_id = self.node_id

        # Initialize leader state
        for node in self._get_other_nodes():
            self.next_index[node["id"]] = len(self.log)
            self.match_index[node["id"]] = -1

        logger.info(f"[{self.node_id}] 👑 Became LEADER for term {self.current_term}")

        # Start sending heartbeats
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
        self.heartbeat_task = asyncio.create_task(self._send_heartbeats())

    async def _step_down(self, new_term: int):
        """
        Step down to FOLLOWER state.

        TRIGGERED WHEN:
            - Receive message from higher term
            - Discover another leader exists
            - Election timeout while CANDIDATE
        """
        old_state = self.state
        self.state = RaftState.FOLLOWER
        self.current_term = new_term
        self.voted_for = None
        self.leader_id = None

        # Cancel heartbeat task if was leader
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
            self.heartbeat_task = None

        logger.info(f"[{self.node_id}] Stepped down from {old_state} to FOLLOWER (term={new_term})")

        # Reset election timeout
        self.last_heartbeat_time = asyncio.get_event_loop().time()
        self.election_timeout = self._random_election_timeout()

    async def _send_heartbeats(self):
        """
        Send periodic heartbeats to all followers.

        HEARTBEAT = AppendEntries RPC with no entries

        PURPOSE:
            1. Prevent followers from timing out
            2. Establish leadership
            3. Replicate log entries

        INTERVAL: Every 1 second (faster than election timeout)
        """
        while self.state == RaftState.LEADER:
            logger.debug(f"[{self.node_id}] 💓 Sending heartbeats (term={self.current_term})")

            async with httpx.AsyncClient(timeout=2.0) as client:
                for node in self._get_other_nodes():
                    try:
                        # Get entries to send to this follower
                        next_idx = self.next_index.get(node["id"], len(self.log))
                        entries = self.log[next_idx:] if next_idx < len(self.log) else []

                        response = await client.post(
                            f"{node['url']}/api/raft/append_entries",
                            json={
                                "term": self.current_term,
                                "leader_id": self.node_id,
                                "prev_log_index": next_idx - 1,
                                "prev_log_term": self.log[next_idx - 1].term if next_idx > 0 else 0,
                                "entries": [e.to_dict() for e in entries],
                                "leader_commit": self.commit_index,
                            }
                        )

                        if response.status_code == 200:
                            data = response.json()

                            # If follower has higher term, step down
                            if data.get("term", 0) > self.current_term:
                                await self._step_down(data["term"])
                                return

                            # Update follower indices if successful
                            if data.get("success"):
                                if entries:
                                    self.match_index[node["id"]] = next_idx + len(entries) - 1
                                    self.next_index[node["id"]] = next_idx + len(entries)
                            else:
                                # Decrement next_index and retry
                                self.next_index[node["id"]] = max(0, next_idx - 1)

                    except Exception as e:
                        logger.warning(f"[{self.node_id}] Heartbeat to {node['id']} failed: {e}")

            # Send heartbeats every 1 second
            await asyncio.sleep(1.0)

    async def handle_request_vote(self, request: dict) -> dict:
        """
        Handle RequestVote RPC from candidate.

        VOTE GRANTED IF:
            1. Candidate's term >= our term
            2. We haven't voted in this term (or voted for this candidate)
            3. Candidate's log is at least as up-to-date as ours

        TEACHING NOTE:
            This prevents voting for outdated candidates
            Ensures elected leader has all committed entries
        """
        candidate_term = request["term"]
        candidate_id = request["candidate_id"]

        # If candidate has higher term, step down
        if candidate_term > self.current_term:
            await self._step_down(candidate_term)

        # Check if we can grant vote
        vote_granted = False

        if candidate_term < self.current_term:
            # Candidate is outdated
            vote_granted = False
            logger.info(f"[{self.node_id}] ✗ Rejected vote for {candidate_id} (term {candidate_term} < {self.current_term})")

        elif self.voted_for is None or self.voted_for == candidate_id:
            # Haven't voted yet in this term, or already voted for this candidate
            vote_granted = True
            self.voted_for = candidate_id
            logger.info(f"[{self.node_id}] ✓ Granted vote to {candidate_id} for term {candidate_term}")

        else:
            # Already voted for someone else
            vote_granted = False
            logger.info(f"[{self.node_id}] ✗ Rejected vote for {candidate_id} (already voted for {self.voted_for})")

        return {
            "term": self.current_term,
            "vote_granted": vote_granted,
        }

    async def handle_append_entries(self, request: dict) -> dict:
        """
        Handle AppendEntries RPC from leader.

        PURPOSES:
            1. Heartbeat (empty entries)
            2. Log replication (with entries)

        BEHAVIOR:
            - Reset election timeout (received heartbeat)
            - Append new entries to log
            - Update commit index
            - Return success/failure
        """
        leader_term = request["term"]
        leader_id = request["leader_id"]
        entries = request.get("entries", [])

        # If leader has higher term, step down
        if leader_term > self.current_term:
            await self._step_down(leader_term)

        # Reject if term is outdated
        if leader_term < self.current_term:
            return {
                "term": self.current_term,
                "success": False,
            }

        # Valid leader - reset election timeout
        self.last_heartbeat_time = asyncio.get_event_loop().time()
        self.leader_id = leader_id

        if self.state != RaftState.FOLLOWER:
            await self._step_down(leader_term)

        # Append entries to log
        if entries:
            for entry_dict in entries:
                log_entry = LogEntry(
                    term=entry_dict["term"],
                    command=entry_dict["command"],
                    key=entry_dict["key"],
                    value=entry_dict.get("value"),
                )
                self.log.append(log_entry)

            logger.info(f"[{self.node_id}] Appended {len(entries)} entries from leader {leader_id}")

        # Update commit index
        leader_commit = request.get("leader_commit", -1)
        if leader_commit > self.commit_index:
            self.commit_index = min(leader_commit, len(self.log) - 1)

            # Apply newly committed entries to state machine (KV store)
            await self._apply_committed_entries()

        return {
            "term": self.current_term,
            "success": True,
        }

    async def replicate_command(self, command: str, key: str, value: Optional[str] = None) -> bool:
        """
        Replicate a command (PUT/DELETE) through Raft consensus.

        PROCESS:
            1. Leader appends entry to its log
            2. Leader sends entry to all followers (via heartbeat)
            3. Once majority replicates → commit the entry
            4. Apply to state machine (KV store)

        Returns: True if successfully replicated, False otherwise
        """
        if self.state != RaftState.LEADER:
            logger.error(f"[{self.node_id}] Cannot replicate - not leader")
            return False

        # Append to leader's log
        log_entry = LogEntry(
            term=self.current_term,
            command=command,
            key=key,
            value=value,
        )
        self.log.append(log_entry)
        log_index = len(self.log) - 1

        logger.info(f"[{self.node_id}] Replicating {command} {key} via Raft (index={log_index})")

        # Heartbeat will replicate this entry
        # In production, would wait for replication here
        # For simplicity, we assume next heartbeat will replicate

        # Commit immediately for this demo (simplified)
        # In real Raft, wait for majority replication
        self.commit_index = log_index

        return True

    async def _apply_committed_entries(self):
        """
        Apply committed log entries to the state machine (KV store).

        This is called when commit_index is updated, either:
        - On the leader after replicating to majority
        - On followers when receiving leader's commit_index

        TEACHING NOTE:
            This is where the Raft log becomes "real" - entries are applied
            to the actual KV store, making the data visible to GET requests.
        """
        if not self.kv_store:
            return  # No KV store attached (shouldn't happen in normal operation)

        # Apply all entries from last_applied+1 to commit_index
        while self.last_applied < self.commit_index:
            self.last_applied += 1
            entry = self.log[self.last_applied]

            logger.info(f"[{self.node_id}] Applying log entry {self.last_applied}: {entry.command} {entry.key}")

            # Apply the command to the KV store
            if entry.command == "PUT":
                self.kv_store.put(entry.key, entry.value)
                logger.info(f"[{self.node_id}] ✓ Applied PUT {entry.key}={entry.value}")

            elif entry.command == "DELETE":
                self.kv_store.delete(entry.key)
                logger.info(f"[{self.node_id}] ✓ Applied DELETE {entry.key}")

    def get_state(self) -> dict:
        """
        Get current Raft state for dashboard visualization.

        RETURNS:
            - state: follower/candidate/leader
            - term: current term number
            - leader_id: who is the leader
            - log_size: number of log entries
            - commit_index: highest committed entry
        """
        return {
            "node_id": self.node_id,
            "state": self.state.value,
            "term": self.current_term,
            "leader_id": self.leader_id,
            "voted_for": self.voted_for,
            "log_size": len(self.log),
            "commit_index": self.commit_index,
            "last_heartbeat": self.last_heartbeat_time,
        }
