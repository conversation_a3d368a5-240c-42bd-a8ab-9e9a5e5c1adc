"""
==============================================================================
SHARDING UTILITIES - Data Partitioning and Distribution
==============================================================================

Implements different sharding strategies for horizontal data distribution.

SHARDING STRATEGIES:
    1. Hash-based (Modulo):      hash(key) % num_nodes
    2. Consistent Hashing:       Uses hash ring for better rebalancing
    3. Range-based:              Key ranges assigned to nodes

TEACHING CONCEPTS:
    - Horizontal scaling (add nodes to increase capacity)
    - Data partitioning (split data across nodes)
    - Shard routing (determine which node owns a key)
    - Rebalancing (redistribute when nodes change)

==============================================================================
"""

import hashlib
from typing import List, Optional, Dict
import logging

logger = logging.getLogger(__name__)


class ShardRouter:
    """
    Routes keys to appropriate shards (nodes) based on sharding strategy.

    STRATEGIES:
        - hash_modulo:      Simple hash(key) % num_nodes
        - consistent_hash:  Hash ring with virtual nodes
        - range:            Key ranges (e.g., a-m → node-1, n-z → node-2)
    """

    def __init__(self, strategy: str = "hash_modulo", nodes: List[str] = None):
        """
        Initialize shard router.

        Args:
            strategy: Sharding strategy ("hash_modulo", "consistent_hash", "range")
            nodes: List of node IDs (e.g., ["node-1", "node-2"])
        """
        self.strategy = strategy
        self.nodes = nodes or []
        self.virtual_nodes_count = 150  # For consistent hashing
        self.hash_ring: Dict[int, str] = {}

        if strategy == "consistent_hash":
            self._build_hash_ring()

        logger.info(f"ShardRouter initialized: strategy={strategy}, nodes={nodes}")

    def add_node(self, node_id: str):
        """Add a node to the shard cluster."""
        if node_id not in self.nodes:
            self.nodes.append(node_id)
            if self.strategy == "consistent_hash":
                self._build_hash_ring()
            logger.info(f"Node added to shard cluster: {node_id}")

    def remove_node(self, node_id: str):
        """Remove a node from the shard cluster."""
        if node_id in self.nodes:
            self.nodes.remove(node_id)
            if self.strategy == "consistent_hash":
                self._build_hash_ring()
            logger.info(f"Node removed from shard cluster: {node_id}")

    def get_shard(self, key: str) -> Optional[str]:
        """
        Determine which node/shard should store this key.

        Returns:
            node_id (e.g., "node-1") or None if no nodes available

        EXAMPLES:
            Hash Modulo:
                get_shard("user:1") → "node-1"  (hash("user:1") % 2 = 1)
                get_shard("user:2") → "node-2"  (hash("user:2") % 2 = 0)

            Consistent Hash:
                Uses hash ring to minimize rebalancing when nodes change
        """
        if not self.nodes:
            return None

        if self.strategy == "hash_modulo":
            return self._hash_modulo_shard(key)
        elif self.strategy == "consistent_hash":
            return self._consistent_hash_shard(key)
        elif self.strategy == "range":
            return self._range_shard(key)
        else:
            # Default: return first node
            return self.nodes[0]

    def _hash_modulo_shard(self, key: str) -> str:
        """
        Simple modulo-based sharding.

        ALGORITHM:
            shard_index = hash(key) % num_nodes
            return nodes[shard_index]

        PROS:
            - Simple to implement
            - Even distribution

        CONS:
            - Rebalancing requires moving ~all data when nodes change
            - Not resilient to cluster size changes

        EXAMPLE:
            2 nodes: ["node-1", "node-2"]
            hash("user:1") = 12345 → 12345 % 2 = 1 → "node-2"
            hash("user:2") = 67890 → 67890 % 2 = 0 → "node-1"
        """
        key_hash = int(hashlib.md5(key.encode()).hexdigest(), 16)
        shard_index = key_hash % len(self.nodes)
        return self.nodes[shard_index]

    def _consistent_hash_shard(self, key: str) -> str:
        """
        Consistent hashing with virtual nodes.

        ALGORITHM:
            1. Build hash ring with virtual nodes
            2. Hash the key
            3. Find next node clockwise on ring

        PROS:
            - Minimal rebalancing (only K/n keys move when adding node)
            - Better distribution with virtual nodes

        CONS:
            - More complex implementation
            - Slightly more overhead

        TEACHING NOTE:
            Used by Cassandra, DynamoDB, Riak
        """
        if not self.hash_ring:
            self._build_hash_ring()

        key_hash = int(hashlib.md5(key.encode()).hexdigest(), 16)

        # Find the next node on the ring (clockwise)
        for ring_hash in sorted(self.hash_ring.keys()):
            if key_hash <= ring_hash:
                return self.hash_ring[ring_hash]

        # Wrap around to first node
        return self.hash_ring[min(self.hash_ring.keys())]

    def _build_hash_ring(self):
        """
        Build hash ring with virtual nodes for consistent hashing.

        VIRTUAL NODES:
            Each physical node gets multiple positions on the ring.
            This improves distribution and makes rebalancing smoother.

        EXAMPLE:
            2 physical nodes × 150 virtual nodes = 300 ring positions
        """
        self.hash_ring = {}

        for node in self.nodes:
            for i in range(self.virtual_nodes_count):
                virtual_key = f"{node}:{i}"
                virtual_hash = int(hashlib.md5(virtual_key.encode()).hexdigest(), 16)
                self.hash_ring[virtual_hash] = node

        logger.info(f"Hash ring built: {len(self.hash_ring)} virtual nodes for {len(self.nodes)} physical nodes")

    def _range_shard(self, key: str) -> str:
        """
        Range-based sharding.

        ALGORITHM:
            Assign key ranges to nodes:
            - a-m → node-1
            - n-z → node-2

        PROS:
            - Good for range queries
            - Ordered data

        CONS:
            - Uneven distribution (hotspots)
            - Manual range definition

        EXAMPLE:
            key="apple"  → first char='a' → node-1
            key="zebra"  → first char='z' → node-2
        """
        if not key:
            return self.nodes[0]

        first_char = key[0].lower()

        # Simple 2-way split: a-m vs n-z
        if len(self.nodes) == 2:
            return self.nodes[0] if first_char <= 'm' else self.nodes[1]

        # For more nodes, divide alphabet evenly
        char_value = ord(first_char) - ord('a')
        if char_value < 0 or char_value > 25:
            # Non-alphabetic, use modulo
            char_value = ord(first_char)

        shard_index = (char_value * len(self.nodes)) // 26
        return self.nodes[min(shard_index, len(self.nodes) - 1)]

    def get_all_shards_for_keys(self, keys: List[str]) -> Dict[str, List[str]]:
        """
        Group keys by their target shards.

        Returns:
            {"node-1": ["user:1", "user:3"], "node-2": ["user:2", "user:4"]}

        Used for:
            - Bulk operations
            - Rebalancing
            - Migration
        """
        shards = {}

        for key in keys:
            shard = self.get_shard(key)
            if shard:
                if shard not in shards:
                    shards[shard] = []
                shards[shard].append(key)

        return shards

    def get_stats(self) -> dict:
        """Get sharding statistics."""
        return {
            "strategy": self.strategy,
            "total_nodes": len(self.nodes),
            "nodes": self.nodes,
            "virtual_nodes": len(self.hash_ring) if self.strategy == "consistent_hash" else 0,
        }
