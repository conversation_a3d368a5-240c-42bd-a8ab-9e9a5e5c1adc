"""
==============================================================================
DISTRIBUTED KEY-VALUE STORE - Demo Application
==============================================================================

FastAPI application demonstrating distributed systems concepts through
progressive feature toggles.

ARCHITECTURE EVOLUTION:

    PHASE 0: MONOLITH (Single Point of Failure)
    ┌──────────────────┐
    │    Node 1        │
    │  ┌───────────┐   │
    │  │ KV Store  │   │  ← All data here
    │  │ {a:1,b:2} │   │
    │  └───────────┘   │
    └──────────────────┘
    Problem: If this node dies → data is lost

    PHASE 1: SECOND NODE (Unsynchronized)
    ┌──────────────┐          ┌──────────────┐
    │   Node 1     │          │   Node 2     │
    │ ┌─────────┐  │          │ ┌─────────┐  │
    │ │{a:1}    │  │  ✗ NO    │ │{b:2}    │  │
    │ └─────────┘  │   SYNC   │ └─────────┘  │
    └──────────────┘          └──────────────┘
    Problem: Data is NOT synchronized between nodes

    PHASE 2: REPLICATION (Synchronized) [FUTURE]
    ┌──────────────┐          ┌──────────────┐
    │   Leader     │ -------> │  Follower    │
    │ ┌─────────┐  │ replicate│ ┌─────────┐  │
    │ │{a:1,b:2}│  │ -------> │ │{a:1,b:2}│  │
    │ └─────────┘  │          │ └─────────┘  │
    └──────────────┘          └──────────────┘
    Solution: Leader replicates writes to followers

ENDPOINTS:
    API:        http://localhost:8001/api/kv/{key}
    Dashboard:  http://localhost:3000
    Features:   http://localhost:8001/api/features

DOCKER COMPOSE:
    docker-compose up                           # Start node-1
    docker-compose --profile with-node-2 up    # Start node-1 + node-2

==============================================================================
"""

from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import Optional, List
import logging
import os
import asyncio
import httpx
from datetime import datetime

from config import FeatureFlags
from kv_store import KeyValueStore
from raft import RaftNode, RaftState
from sharding import ShardRouter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Distributed KV Store Demo",
    description="Educational demo showing distributed systems concepts through feature toggles",
    version="1.0.0"
)

# Add CORS middleware to allow requests from dashboard
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for demo purposes
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods (GET, POST, PUT, DELETE, OPTIONS)
    allow_headers=["*"],  # Allow all headers
)

# Node configuration from environment
NODE_ID = os.getenv("NODE_ID", "node-1")
PORT = int(os.getenv("PORT", "8000"))

# Initialize the key-value store for this node
kv_store = KeyValueStore(node_id=NODE_ID)

# Initialize Raft consensus (will start when consensus is enabled)
# Cluster configuration
CLUSTER_NODES = [
    {"id": "node-1", "url": "http://node-1:8000"},
    {"id": "node-2", "url": "http://node-2:8000"},
]
raft_node: Optional[RaftNode] = None

# Initialize shard router (will be configured when sharding is enabled)
shard_router: Optional[ShardRouter] = None

# WebSocket connections for real-time dashboard updates
websocket_connections: List[WebSocket] = []

# Replication log for dashboard
replication_log: List[dict] = []


# ============================================================================
# Pydantic Models
# ============================================================================

class PutRequest(BaseModel):
    key: str
    value: str


class FeatureToggleRequest(BaseModel):
    enabled: bool


# ============================================================================
# Replication Functions
# ============================================================================

def get_follower_nodes():
    """
    Get list of follower nodes to replicate to.

    SIMPLE STRATEGY:
        - node-1 is always the leader (port 8001)
        - node-2 is the follower (port 8002)
    """
    if NODE_ID == "node-1" and FeatureFlags.second_node_enabled:
        return [{"id": "node-2", "url": "http://node-2:8000"}]
    return []


async def replicate_to_followers_sync(key: str, value: str) -> dict:
    """
    SYNCHRONOUS REPLICATION

    BEHAVIOR:
        1. Write to leader (node-1)
        2. Wait for follower (node-2) to acknowledge
        3. Return success only after follower confirms

    DIAGRAM:
        Client → Leader → Follower
                   ↓        ↓
                  WAIT    ACK ✓
                   ↓
                SUCCESS

    PROS: Strong consistency - data guaranteed on both nodes
    CONS: Higher latency - client waits for follower
    """
    followers = get_follower_nodes()
    if not followers:
        return {"replicated_to": [], "mode": "none"}

    log_entry = {
        "timestamp": datetime.utcnow().isoformat(),
        "mode": "SYNC",
        "key": key,
        "status": "replicating",
        "followers": []
    }

    replicated_to = []
    async with httpx.AsyncClient(timeout=5.0) as client:
        for follower in followers:
            try:
                logger.info(f"[SYNC REPLICATION] {NODE_ID} → {follower['id']} (key={key})")

                response = await client.put(
                    f"{follower['url']}/api/kv/{key}",
                    json={"key": key, "value": value}
                )

                if response.status_code == 200:
                    replicated_to.append(follower['id'])
                    log_entry["followers"].append({
                        "node": follower['id'],
                        "status": "success",
                        "duration_ms": 0  # Immediate in sync mode
                    })
                    logger.info(f"[SYNC REPLICATION] ✓ {follower['id']} confirmed")

            except Exception as e:
                logger.error(f"[SYNC REPLICATION] ✗ {follower['id']} failed: {e}")
                log_entry["followers"].append({
                    "node": follower['id'],
                    "status": "failed",
                    "error": str(e)
                })

    log_entry["status"] = "completed"
    replication_log.append(log_entry)

    # Broadcast log update to dashboard
    await broadcast_replication_log()

    return {"replicated_to": replicated_to, "mode": "sync"}


async def replicate_to_followers_async(key: str, value: str):
    """
    ASYNCHRONOUS REPLICATION (Background Task)

    BEHAVIOR:
        1. Write to leader (node-1)
        2. Return success immediately
        3. Replicate to follower in background (with simulated delay)

    DIAGRAM:
        Client → Leader → SUCCESS (immediate)
                   ↓
                Background:
                   ↓ (delay 2s)
                Follower ← replicate

    PROS: Low latency - client doesn't wait
    CONS: Eventual consistency - brief window where nodes differ
    """
    followers = get_follower_nodes()
    if not followers:
        return

    log_entry = {
        "timestamp": datetime.utcnow().isoformat(),
        "mode": "ASYNC",
        "key": key,
        "status": "queued",
        "followers": []
    }
    replication_log.append(log_entry)

    # Simulate network latency
    await asyncio.sleep(FeatureFlags.async_replication_delay)

    async with httpx.AsyncClient(timeout=5.0) as client:
        for follower in followers:
            try:
                start_time = datetime.utcnow()
                logger.info(f"[ASYNC REPLICATION] {NODE_ID} → {follower['id']} (key={key}, delay={FeatureFlags.async_replication_delay}s)")

                response = await client.put(
                    f"{follower['url']}/api/kv/{key}",
                    json={"key": key, "value": value}
                )

                duration_ms = (datetime.utcnow() - start_time).total_seconds() * 1000

                if response.status_code == 200:
                    log_entry["followers"].append({
                        "node": follower['id'],
                        "status": "success",
                        "duration_ms": int(duration_ms)
                    })
                    logger.info(f"[ASYNC REPLICATION] ✓ {follower['id']} confirmed after {int(duration_ms)}ms")

            except Exception as e:
                logger.error(f"[ASYNC REPLICATION] ✗ {follower['id']} failed: {e}")
                log_entry["followers"].append({
                    "node": follower['id'],
                    "status": "failed",
                    "error": str(e)
                })

    log_entry["status"] = "completed"

    # Broadcast log update to dashboard
    await broadcast_replication_log()


async def broadcast_replication_log():
    """Broadcast replication log to dashboard via WebSocket."""
    if not websocket_connections:
        return

    # Send last 10 log entries
    log_data = {
        "type": "replication_log",
        "logs": replication_log[-10:]
    }

    disconnected = []
    for websocket in websocket_connections:
        try:
            await websocket.send_json(log_data)
        except Exception as e:
            logger.error(f"Error broadcasting replication log: {e}")
            disconnected.append(websocket)

    for ws in disconnected:
        websocket_connections.remove(ws)


# ============================================================================
# WebSocket Management
# ============================================================================

async def broadcast_cluster_state():
    """Broadcast current cluster state to all connected WebSocket clients."""
    cluster_state = get_cluster_state()

    disconnected = []
    for websocket in websocket_connections:
        try:
            await websocket.send_json(cluster_state)
        except Exception as e:
            logger.error(f"Error broadcasting to WebSocket: {e}")
            disconnected.append(websocket)

    # Remove disconnected clients
    for ws in disconnected:
        websocket_connections.remove(ws)


async def get_cluster_state() -> dict:
    """
    Get the current state of the cluster for dashboard visualization.

    Teaching note: This shows what data each node has, demonstrating
    synchronization (or lack thereof) across nodes.

    PHASE 3 ADDITION:
        Includes Raft consensus state (leader/follower/candidate)
        Shows current term and leader ID
    """
    # Get Raft state if enabled
    raft_state = None
    if FeatureFlags.consensus_enabled and raft_node:
        raft_state = raft_node.get_state()

    nodes = [
        {
            "id": NODE_ID,
            "status": "active",
            "stats": kv_store.get_stats(),
            "is_primary": NODE_ID == "node-1",
            "raft": raft_state,  # Add Raft state
        }
    ]

    # If second node is enabled, try to query its state
    if FeatureFlags.second_node_enabled and NODE_ID == "node-1":
        try:
            # Try to fetch node-2's stats via HTTP
            async with httpx.AsyncClient(timeout=1.0) as client:
                node2_stats_response = await client.get("http://node-2:8000/api/node/stats")
                node2_stats = node2_stats_response.json() if node2_stats_response.status_code == 200 else None

                node2_raft = None
                if FeatureFlags.consensus_enabled:
                    node2_raft_response = await client.get("http://node-2:8000/api/raft/state")
                    node2_raft_data = node2_raft_response.json() if node2_raft_response.status_code == 200 else None
                    if node2_raft_data and node2_raft_data.get("enabled"):
                        node2_raft = node2_raft_data

                nodes.append({
                    "id": "node-2",
                    "status": "active",
                    "stats": node2_stats if node2_stats else {
                        "node_id": "node-2",
                        "total_keys": "?",
                        "keys": [],
                        "sample_data": {},
                    },
                    "is_primary": False,
                    "raft": node2_raft,
                })
        except Exception as e:
            # Node-2 not reachable, show placeholder
            logger.warning(f"Could not fetch node-2 state: {e}")
            nodes.append({
                "id": "node-2",
                "status": "unknown",
                "stats": {
                    "node_id": "node-2",
                    "total_keys": "?",
                    "keys": [],
                    "sample_data": {},
                },
                "is_primary": False,
                "raft": None,
            })

    return {
        "nodes": nodes,
        "features": FeatureFlags.to_dict(),
        "cluster_size": len(nodes),
        "raft_cluster": {
            "enabled": FeatureFlags.consensus_enabled,
            "leader_id": raft_state.get("leader_id") if raft_state else None,
            "term": raft_state.get("term") if raft_state else 0,
        } if FeatureFlags.consensus_enabled else None,
        "sharding": {
            "enabled": FeatureFlags.sharding_enabled,
            "strategy": FeatureFlags.sharding_strategy,
            "shard_count": len(shard_router.nodes) if shard_router else 0,
        } if FeatureFlags.sharding_enabled else None,
        "timestamp": asyncio.get_event_loop().time(),
    }


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """
    WebSocket endpoint for real-time dashboard updates.
    Clients connect here to receive cluster state changes.
    """
    await websocket.accept()
    websocket_connections.append(websocket)
    logger.info(f"WebSocket client connected. Total connections: {len(websocket_connections)}")

    # Send initial cluster state
    await websocket.send_json(await get_cluster_state())

    try:
        while True:
            # Keep connection alive and handle any incoming messages
            data = await websocket.receive_text()
            logger.info(f"Received WebSocket message: {data}")
    except WebSocketDisconnect:
        websocket_connections.remove(websocket)
        logger.info(f"WebSocket client disconnected. Total connections: {len(websocket_connections)}")


# ============================================================================
# Key-Value Store API Endpoints
# ============================================================================

@app.get("/")
async def root():
    """Root endpoint - shows basic node info."""
    return {
        "message": "Distributed KV Store Demo",
        "node_id": NODE_ID,
        "features": FeatureFlags.to_dict(),
    }


@app.get("/api/kv/{key}")
async def get_value(key: str):
    """
    GET a value by key from this node.

    BEHAVIOR:
        - Looks up key in local storage only
        - Returns 404 if key not found on THIS node

    DISTRIBUTED IMPLICATIONS (Phase 1):
        When second_node_enabled=True but replication_enabled=False:

        ┌─────────┐              ┌─────────┐
        │ Node 1  │              │ Node 2  │
        │ {a: 1}  │  ✗ NO SYNC   │ {b: 2}  │
        └─────────┘              └─────────┘

        GET /api/kv/a on node-1 → 200 OK
        GET /api/kv/a on node-2 → 404 Not Found

        This demonstrates the synchronization problem.

    EXAMPLE:
        http://localhost:8001/api/kv/user:1  (node-1)
        http://localhost:8002/api/kv/user:1  (node-2, may 404)
    """
    # PHASE 4: Check sharding if enabled
    if FeatureFlags.sharding_enabled and shard_router:
        target_shard = shard_router.get_shard(key)

        if target_shard != NODE_ID:
            # This key belongs to a different shard - proxy the request
            target_url = f"http://{target_shard}:8000/api/kv/{key}"

            try:
                async with httpx.AsyncClient(timeout=5.0) as client:
                    response = await client.get(target_url)

                    if response.status_code == 200:
                        data = response.json()
                        data["proxied_from"] = NODE_ID
                        data["shard_owner"] = target_shard
                        return data
                    elif response.status_code == 404:
                        raise HTTPException(status_code=404, detail=f"Key '{key}' not found on shard {target_shard}")
                    else:
                        raise HTTPException(
                            status_code=response.status_code,
                            detail=f"Shard {target_shard} returned error: {response.text}"
                        )
            except httpx.RequestError as e:
                raise HTTPException(
                    status_code=503,
                    detail=f"Shard {target_shard} unavailable: {str(e)}"
                )

    value = kv_store.get(key)

    if value is None:
        raise HTTPException(status_code=404, detail=f"Key '{key}' not found on {NODE_ID}")

    return {
        "key": key,
        "value": value,
        "node_id": NODE_ID,
        "metadata": kv_store.metadata.get(key, {}),
    }


@app.put("/api/kv/{key}")
async def put_value(key: str, request: PutRequest):
    """
    PUT a key-value pair to this node.

    BEHAVIOR (depends on replication/consensus settings):

    PHASE 1 - NO REPLICATION:
        PUT /api/kv/user:1 = "Alice" to node-1
        ↓
        ┌─────────────┐              ┌─────────────┐
        │   Node 1    │              │   Node 2    │
        │ {user:1:    │  ✗ NO SYNC   │             │
        │  "Alice"}   │              │   (empty)   │
        └─────────────┘              └─────────────┘

    PHASE 2 - SYNC REPLICATION:
        Client → Leader → Follower
                   ↓        ↓
                  WAIT    ACK ✓
                   ↓
                SUCCESS (both nodes have data)

        Latency: Higher (waits for follower)
        Consistency: Strong (immediate)

    PHASE 2 - ASYNC REPLICATION:
        Client → Leader → SUCCESS (immediate)
                   ↓
                Background (2s delay):
                   ↓
                Follower ← replicate

        Latency: Lower (doesn't wait)
        Consistency: Eventual (2s window)

    PHASE 3 - RAFT CONSENSUS:
        Client → Leader → Replicate via Raft log
                   ↓
                Commit once majority confirms
                   ↓
                SUCCESS

        Ensures: Leader election, automatic failover
        Consistency: Strong (committed to majority)

    EXAMPLE:
        curl -X PUT http://localhost:8001/api/kv/user:1 \
          -H "Content-Type: application/json" \
          -d '{"key":"user:1","value":"Alice"}'
    """
    start_time = datetime.utcnow()

    # PHASE 4: Check sharding if enabled
    if FeatureFlags.sharding_enabled and shard_router:
        target_shard = shard_router.get_shard(request.key)

        if target_shard != NODE_ID:
            # This key belongs to a different shard - proxy the request
            target_url = f"http://{target_shard}:8000/api/kv/{request.key}"

            try:
                async with httpx.AsyncClient(timeout=5.0) as client:
                    response = await client.put(
                        target_url,
                        json={"key": request.key, "value": request.value}
                    )

                    if response.status_code == 200:
                        data = response.json()
                        data["proxied_from"] = NODE_ID
                        data["shard_owner"] = target_shard
                        return data
                    else:
                        raise HTTPException(
                            status_code=response.status_code,
                            detail=f"Shard {target_shard} returned error: {response.text}"
                        )
            except httpx.RequestError as e:
                raise HTTPException(
                    status_code=503,
                    detail=f"Shard {target_shard} unavailable: {str(e)}"
                )

    # PHASE 3: Use Raft consensus if enabled
    if FeatureFlags.consensus_enabled and raft_node:
        if raft_node.state != RaftState.LEADER:
            # Not the leader - redirect to leader or reject
            if raft_node.leader_id:
                raise HTTPException(
                    status_code=503,
                    detail=f"Not the leader. Current leader: {raft_node.leader_id}. Please redirect to leader."
                )
            else:
                raise HTTPException(
                    status_code=503,
                    detail="No leader elected yet. Cluster is unavailable."
                )

        # Replicate through Raft
        success = await raft_node.replicate_command("PUT", request.key, request.value)

        if not success:
            raise HTTPException(status_code=500, detail="Raft replication failed")

        # Apply to local store immediately (leader writes)
        await raft_node._apply_committed_entries()

        # Get result from KV store
        result = {
            "key": request.key,
            "value": request.value,
            "node_id": NODE_ID,
            "timestamp": datetime.utcnow().isoformat(),
        }
    else:
        # Non-Raft mode: Write to local store directly
        result = kv_store.put(request.key, request.value)

    # PHASE 2: Traditional replication (if enabled and consensus not enabled)
    replication_info = {"mode": "none", "replicated_to": []}

    if FeatureFlags.replication_enabled and not FeatureFlags.consensus_enabled and NODE_ID == "node-1":
        if FeatureFlags.replication_mode == "sync":
            # SYNCHRONOUS: Wait for replication before returning
            replication_info = await replicate_to_followers_sync(request.key, request.value)

        elif FeatureFlags.replication_mode == "async":
            # ASYNCHRONOUS: Return immediately, replicate in background
            asyncio.create_task(replicate_to_followers_async(request.key, request.value))
            replication_info = {"mode": "async", "status": "queued", "note": "Replication in progress (background)"}

    # Add Raft info if consensus enabled
    if FeatureFlags.consensus_enabled and raft_node:
        replication_info = {
            "mode": "raft",
            "term": raft_node.current_term,
            "leader": raft_node.leader_id,
            "log_index": len(raft_node.log) - 1,
        }

    # Calculate response time
    duration_ms = (datetime.utcnow() - start_time).total_seconds() * 1000

    # Broadcast cluster state update to dashboard
    await broadcast_cluster_state()

    return {
        "message": "Key-value pair stored",
        "data": result,
        "replication": replication_info,
        "response_time_ms": int(duration_ms),
        "warning": "Data only stored on current node!" if FeatureFlags.second_node_enabled and not FeatureFlags.replication_enabled and not FeatureFlags.consensus_enabled else None,
    }


@app.delete("/api/kv/{key}")
async def delete_value(key: str):
    """DELETE a key-value pair."""
    success = kv_store.delete(key)

    if not success:
        raise HTTPException(status_code=404, detail=f"Key '{key}' not found on {NODE_ID}")

    # Broadcast cluster state update
    await broadcast_cluster_state()

    return {
        "message": f"Key '{key}' deleted from {NODE_ID}",
        "node_id": NODE_ID,
    }


@app.get("/api/kv")
async def list_keys():
    """List all keys in the store (on this node)."""
    return {
        "node_id": NODE_ID,
        "keys": kv_store.get_all_keys(),
        "total": kv_store.size(),
    }


# ============================================================================
# Raft Consensus API
# ============================================================================

@app.post("/api/raft/request_vote")
async def raft_request_vote(request: dict):
    """
    Handle RequestVote RPC from Raft candidate.

    Called during leader election when a candidate requests our vote.

    RAFT VOTING RULES:
        - Grant vote if candidate's term >= our term
        - Only vote once per term
        - Candidate's log must be up-to-date

    EXAMPLE:
        curl -X POST http://localhost:8001/api/raft/request_vote \
          -H "Content-Type: application/json" \
          -d '{"term": 2, "candidate_id": "node-2", "last_log_index": 5, "last_log_term": 1}'
    """
    if not FeatureFlags.consensus_enabled or not raft_node:
        raise HTTPException(status_code=400, detail="Consensus not enabled")

    result = await raft_node.handle_request_vote(request)
    return result


@app.post("/api/raft/append_entries")
async def raft_append_entries(request: dict):
    """
    Handle AppendEntries RPC from Raft leader.

    Used for both:
        1. Heartbeats (empty entries) - prevent election timeouts
        2. Log replication (with entries) - replicate data

    RAFT APPEND RULES:
        - Accept if leader's term >= our term
        - Reset election timeout (received heartbeat)
        - Append entries to log
        - Update commit index

    EXAMPLE:
        curl -X POST http://localhost:8001/api/raft/append_entries \
          -H "Content-Type: application/json" \
          -d '{"term": 2, "leader_id": "node-1", "prev_log_index": 4, "prev_log_term": 1, "entries": [], "leader_commit": 3}'
    """
    if not FeatureFlags.consensus_enabled or not raft_node:
        raise HTTPException(status_code=400, detail="Consensus not enabled")

    result = await raft_node.handle_append_entries(request)
    return result


@app.get("/api/raft/state")
async def get_raft_state():
    """
    Get current Raft consensus state.

    RETURNS:
        - state: follower/candidate/leader
        - term: current term number
        - leader_id: who is the current leader
        - log_size: number of log entries
        - commit_index: highest committed entry

    USE CASE:
        Dashboard visualization
        Debugging cluster state
        Monitoring leader elections

    EXAMPLE:
        curl http://localhost:8001/api/raft/state
    """
    if not FeatureFlags.consensus_enabled or not raft_node:
        return {
            "enabled": False,
            "message": "Consensus not enabled"
        }

    return {
        "enabled": True,
        **raft_node.get_state()
    }


@app.get("/api/raft/log")
async def get_raft_log():
    """
    Get Raft replicated log entries.

    Shows the append-only log of all operations.
    Each entry has: term, command, key, value, timestamp

    TEACHING USE:
        - Show students how Raft maintains ordered log
        - Demonstrate log replication across nodes
        - Compare logs before/after leader election

    EXAMPLE:
        curl http://localhost:8001/api/raft/log
    """
    if not FeatureFlags.consensus_enabled or not raft_node:
        raise HTTPException(status_code=400, detail="Consensus not enabled")

    return {
        "node_id": NODE_ID,
        "log_size": len(raft_node.log),
        "commit_index": raft_node.commit_index,
        "entries": [entry.to_dict() for entry in raft_node.log[-20:]],  # Last 20 entries
    }


# ============================================================================
# Feature Toggle API (Control Plane)
# ============================================================================

@app.get("/api/features")
async def get_features():
    """Get current feature flag configuration."""
    return {
        "features": FeatureFlags.to_dict(),
        "node_id": NODE_ID,
    }


@app.post("/api/features/second_node")
async def toggle_second_node(request: FeatureToggleRequest):
    """
    Toggle the second node feature (Phase 1).

    WHEN ENABLED:
        ┌──────────┐     ┌──────────┐
        │  Node 1  │     │  Node 2  │
        │          │  ✗  │          │  ← NO synchronization
        └──────────┘     └──────────┘

        - Start node-2 with: docker-compose --profile with-node-2 up
        - Both nodes run independently
        - Data is NOT synchronized
        - Demonstrates the distributed systems problem

    WHEN DISABLED:
        ┌──────────┐
        │  Node 1  │  ← Only node
        └──────────┘

    EXAMPLE:
        curl -X POST http://localhost:8001/api/features/second_node \
          -H "Content-Type: application/json" \
          -d '{"enabled": true}'
    """
    FeatureFlags.second_node_enabled = request.enabled

    logger.info(f"Feature 'second_node' toggled to: {request.enabled}")

    # Broadcast updated cluster state
    await broadcast_cluster_state()

    return {
        "message": f"Second node {'enabled' if request.enabled else 'disabled'}",
        "features": FeatureFlags.to_dict(),
        "note": (
            "WARNING: Second node is now active but NOT synchronized with node-1! "
            "Try writing to node-1 and reading from node-2 - you'll see the problem."
        ) if request.enabled else "Second node disabled - back to monolith mode",
    }


@app.post("/api/features/replication")
async def toggle_replication(request: FeatureToggleRequest):
    """
    Toggle replication feature (Phase 2).

    WHEN ENABLED:
        ┌──────────┐  replicate  ┌──────────┐
        │  Leader  │ ----------> │ Follower │
        │ {a: 1}   │ ----------> │ {a: 1}   │
        └──────────┘             └──────────┘

        - Leader (node-1) receives writes
        - Followers (node-2) receive replicated data
        - All nodes have same data
        - Solves the synchronization problem from Phase 1

    REPLICATION MODES:
        - sync:  Client waits for follower ACK (strong consistency)
        - async: Client returns immediately (eventual consistency)

    EXAMPLE:
        curl -X POST http://localhost:8001/api/features/replication \
          -H "Content-Type: application/json" \
          -d '{"enabled": true}'
    """
    if request.enabled and not FeatureFlags.second_node_enabled:
        raise HTTPException(
            status_code=400,
            detail="Cannot enable replication without second node. Enable second_node first!"
        )

    FeatureFlags.replication_enabled = request.enabled
    logger.info(f"Feature 'replication' toggled to: {request.enabled}")

    await broadcast_cluster_state()

    return {
        "message": f"Replication {'enabled' if request.enabled else 'disabled'}",
        "features": FeatureFlags.to_dict(),
        "mode": FeatureFlags.replication_mode,
        "note": f"Using {FeatureFlags.replication_mode} replication" if request.enabled else None,
    }


@app.post("/api/features/replication_mode")
async def set_replication_mode(mode: str):
    """
    Set replication mode: sync or async.

    SYNC REPLICATION:
        Client → Leader → Follower
                   ↓        ↓
                  WAIT    ACK ✓
                   ↓
                SUCCESS

        Pros: Strong consistency
        Cons: Higher latency (waits for follower)

    ASYNC REPLICATION:
        Client → Leader → SUCCESS (immediate)
                   ↓
                Background (2s delay):
                   ↓
                Follower ← replicate

        Pros: Low latency (doesn't wait)
        Cons: Eventual consistency (2s window)

    EXAMPLE:
        curl -X POST http://localhost:8001/api/features/replication_mode?mode=async
    """
    if mode not in ["sync", "async"]:
        raise HTTPException(status_code=400, detail="Mode must be 'sync' or 'async'")

    FeatureFlags.replication_mode = mode
    logger.info(f"Replication mode set to: {mode}")

    await broadcast_cluster_state()

    return {
        "message": f"Replication mode set to {mode}",
        "mode": mode,
        "features": FeatureFlags.to_dict(),
    }


@app.post("/api/features/consensus")
async def toggle_consensus(request: FeatureToggleRequest):
    """
    Toggle Raft consensus feature (Phase 3).

    WHEN ENABLED:
        ┌──────────┐  heartbeat  ┌──────────┐
        │  LEADER  │ ─────────>  │ FOLLOWER │
        │ (node-1) │ <──────────  │ (node-2) │
        └──────────┘    vote      └──────────┘

        - Automatic leader election
        - Heartbeat mechanism prevents timeouts
        - Log-based replication
        - Automatic failover on leader failure

    RAFT GUARANTEES:
        1. Leader Election: Prevents split-brain
        2. Log Replication: Ensures consistency
        3. Safety: Committed entries never lost

    DEMO SCENARIOS:
        1. Start cluster → watch leader election
        2. Kill leader → observe automatic failover
        3. Restart old leader → becomes follower

    EXAMPLE:
        curl -X POST http://localhost:8001/api/features/consensus \
          -H "Content-Type: application/json" \
          -d '{"enabled": true}'
    """
    global raft_node

    if request.enabled and not FeatureFlags.second_node_enabled:
        raise HTTPException(
            status_code=400,
            detail="Cannot enable consensus without second node. Enable second_node first!"
        )

    FeatureFlags.consensus_enabled = request.enabled
    logger.info(f"Feature 'consensus' toggled to: {request.enabled}")

    if request.enabled:
        # Initialize and start Raft
        if not raft_node:
            raft_node = RaftNode(node_id=NODE_ID, cluster_nodes=CLUSTER_NODES, kv_store=kv_store)
            await raft_node.start()
            logger.info(f"[{NODE_ID}] Raft consensus started")
    else:
        # Stop Raft
        if raft_node:
            await raft_node.stop()
            raft_node = None
            logger.info(f"[{NODE_ID}] Raft consensus stopped")

    await broadcast_cluster_state()

    return {
        "message": f"Consensus {'enabled' if request.enabled else 'disabled'}",
        "features": FeatureFlags.to_dict(),
        "raft_state": raft_node.get_state() if raft_node else None,
        "note": (
            "Raft consensus enabled! Leader election will start automatically. "
            "Watch the dashboard for leader/follower states."
        ) if request.enabled else "Raft consensus disabled",
    }


class ShardingConfigRequest(BaseModel):
    enabled: bool
    strategy: Optional[str] = "hash_modulo"  # "hash_modulo", "consistent_hash", "range"


@app.post("/api/features/sharding")
async def toggle_sharding(request: ShardingConfigRequest):
    """
    Toggle sharding feature (Phase 4).

    WHEN ENABLED:
        ┌──────────┐         ┌──────────┐
        │ Shard 1  │         │ Shard 2  │
        │ a-m keys │         │ n-z keys │
        └──────────┘         └──────────┘

        Data is partitioned across nodes based on sharding strategy

    STRATEGIES:
        - hash_modulo:      hash(key) % num_nodes
        - consistent_hash:  Hash ring for minimal rebalancing
        - range:            Key ranges (a-m, n-z)

    EXAMPLE:
        curl -X POST http://localhost:8001/api/features/sharding \
          -H "Content-Type: application/json" \
          -d '{"enabled": true, "strategy": "hash_modulo"}'
    """
    global shard_router

    if request.enabled and not FeatureFlags.second_node_enabled:
        raise HTTPException(
            status_code=400,
            detail="Cannot enable sharding without second node. Enable second_node first!"
        )

    FeatureFlags.sharding_enabled = request.enabled
    FeatureFlags.sharding_strategy = request.strategy if request.enabled else "none"

    logger.info(f"Feature 'sharding' toggled to: {request.enabled}, strategy: {request.strategy}")

    if request.enabled:
        # Initialize shard router
        active_nodes = ["node-1"]
        if FeatureFlags.second_node_enabled:
            active_nodes.append("node-2")

        shard_router = ShardRouter(strategy=request.strategy, nodes=active_nodes)
        logger.info(f"[{NODE_ID}] Shard router initialized with {len(active_nodes)} nodes")
    else:
        # Disable sharding
        shard_router = None
        logger.info(f"[{NODE_ID}] Sharding disabled")

    await broadcast_cluster_state()

    return {
        "message": f"Sharding {'enabled' if request.enabled else 'disabled'}",
        "features": FeatureFlags.to_dict(),
        "shard_info": shard_router.get_stats() if shard_router else None,
        "note": (
            f"Sharding enabled with {request.strategy} strategy! "
            f"Keys will be distributed across {len(active_nodes)} nodes."
        ) if request.enabled else "Sharding disabled",
    }


@app.get("/api/sharding/state")
async def get_sharding_state():
    """
    Get current sharding configuration and statistics.

    EXAMPLE:
        curl http://localhost:8001/api/sharding/state
    """
    if not FeatureFlags.sharding_enabled or not shard_router:
        return {
            "enabled": False,
            "message": "Sharding not enabled"
        }

    return {
        "enabled": True,
        "strategy": FeatureFlags.sharding_strategy,
        **shard_router.get_stats()
    }


@app.get("/api/sharding/key/{key}")
async def get_key_shard(key: str):
    """
    Determine which shard owns a specific key.

    EXAMPLE:
        curl http://localhost:8001/api/sharding/key/user:1
    """
    if not FeatureFlags.sharding_enabled or not shard_router:
        raise HTTPException(status_code=400, detail="Sharding not enabled")

    target_shard = shard_router.get_shard(key)

    return {
        "key": key,
        "shard": target_shard,
        "strategy": FeatureFlags.sharding_strategy,
        "current_node": NODE_ID,
        "is_local": target_shard == NODE_ID
    }


@app.get("/api/replication/logs")
async def get_replication_logs():
    """
    Get replication logs showing sync vs async behavior.

    LOG ENTRY FORMAT:
        {
            "timestamp": "2025-10-04T...",
            "mode": "SYNC" | "ASYNC",
            "key": "user:1",
            "status": "completed",
            "followers": [
                {
                    "node": "node-2",
                    "status": "success",
                    "duration_ms": 150
                }
            ]
        }

    Shows the timing difference between sync and async replication.

    EXAMPLE:
        curl http://localhost:8001/api/replication/logs
    """
    return {
        "logs": replication_log[-20:],  # Last 20 entries
        "total": len(replication_log),
    }


@app.post("/api/reset")
async def reset_cluster():
    """
    Reset the entire cluster to initial state.

    WHAT THIS DOES:
        1. Clears all data from current node
        2. Resets all feature flags to Phase 0 (monolith)
        3. Broadcasts updated state to dashboard

    USE CASE:
        - Demo broke and you need fresh start
        - Want to restart from Phase 0
        - Clear all test data

    CLUSTER STATE AFTER RESET:
        ┌──────────┐
        │  Node 1  │  ← Back to monolith
        │ (empty)  │
        └──────────┘

        Features: All disabled
        Data: Cleared

    EXAMPLE:
        curl -X POST http://localhost:8001/api/reset

    DASHBOARD:
        Click "Reset" button: http://localhost:3000
    """
    # Clear data on this node
    kv_store.clear()

    # Reset all feature flags
    FeatureFlags.reset()

    # Clear replication logs
    replication_log.clear()

    logger.info(f"[{NODE_ID}] Cluster reset to initial state")

    # Broadcast updated state
    await broadcast_cluster_state()

    return {
        "message": "Cluster reset to initial state",
        "node_id": NODE_ID,
        "features": FeatureFlags.to_dict(),
        "data_cleared": True,
        "logs_cleared": True,
        "note": "All nodes should be restarted for complete reset. Run: docker-compose restart"
    }


# ============================================================================
# Cluster State API (for dashboard)
# ============================================================================

@app.get("/api/cluster/state")
async def get_cluster_state_api():
    """Get the current cluster state (for dashboard)."""
    return await get_cluster_state()


@app.get("/api/node/stats")
async def get_node_stats():
    """Get statistics for this specific node."""
    return kv_store.get_stats()


# ============================================================================
# Health Check
# ============================================================================

@app.get("/health")
async def health_check():
    """Health check endpoint for Docker/K8s."""
    return {
        "status": "healthy",
        "node_id": NODE_ID,
        "port": PORT,
    }


# ============================================================================
# Startup Event
# ============================================================================

@app.on_event("startup")
async def startup_event():
    """Log startup information."""
    logger.info(f"=" * 60)
    logger.info(f"Distributed KV Store - {NODE_ID} starting on port {PORT}")
    logger.info(f"=" * 60)
    logger.info(f"Features: {FeatureFlags.to_dict()}")
    logger.info(f"=" * 60)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=PORT)
