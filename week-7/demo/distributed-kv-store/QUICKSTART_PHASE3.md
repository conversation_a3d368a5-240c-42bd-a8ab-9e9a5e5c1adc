# Quick Start: Phase 3 - Raft Consensus

**Goal:** See automatic leader election and failover in action in under 5 minutes!

---

## Prerequisites

- <PERSON><PERSON> and <PERSON>er Compose installed
- Ports 8001, 8002, 3000 available

---

## Step 1: Start the Cluster (1 minute)

```bash
cd week-7/demo/distributed-kv-store

# Start both nodes
docker-compose --profile with-node-2 up --build -d

# Verify both running
docker ps
# Should see: kv-store-node-1 and kv-store-node-2
```

---

## Step 2: Open Dashboard (30 seconds)

```bash
# Open in browser
http://localhost:3000
```

You should see:
- Feature Toggles panel
- Cluster State panel
- Two nodes: node-1 and node-2

---

## Step 3: Enable Consensus (1 minute)

**In the dashboard:**

1. Click **"Second Node"** toggle → ON
2. Click **"Consensus (Raft)"** toggle → ON

**Watch the magic happen:**
- Raft info panel appears
- Nodes start as FOLLOWERS (👤)
- After 3-5 seconds, one becomes LEADER (👑)

```
Initial:  node-1: 👤 FOLLOWER  |  node-2: 👤 FOLLOWER
After 5s: node-1: 👑 LEADER    |  node-2: 👤 FOLLOWER
```

---

## Step 4: Test Write Operations (1 minute)

**In the Test Panel (bottom of dashboard):**

1. Ensure "Test on Node 1" is selected (the LEADER)
2. Key: `user:1`
3. Value: `Alice`
4. Click **PUT**

**Expected result:**
```json
{
  "message": "Key-value pair stored",
  "replication": {
    "mode": "raft",
    "term": 1,
    "leader": "node-1",
    "log_index": 0
  }
}
```

5. Click **GET** → should return `Alice`

---

## Step 5: Demonstrate Automatic Failover (2 minutes) ⭐

**This is the coolest part!**

### Kill the Leader:

```bash
# In terminal
docker stop kv-store-node-1
```

### Watch the Dashboard:

```
t=0s : node-1 dies (👑 LEADER disappears)
t=1s : node-2 still FOLLOWER (👤) - waiting for heartbeat
t=3s : node-2 times out - no heartbeat received
t=4s : node-2 becomes CANDIDATE (🗳️) - requesting votes
t=5s : node-2 becomes LEADER (👑) - won election!
```

**System is back online in ~5 seconds!**

### Test the New Leader:

1. Switch to "Test on Node 2" (now the LEADER)
2. Key: `user:2`
3. Value: `Bob`
4. Click **PUT** → Success!

---

## Step 6: Old Leader Returns (1 minute)

```bash
# Restart old leader
docker start kv-store-node-1
```

**Watch the dashboard:**
- node-1 comes back as FOLLOWER (👤)
- node-2 stays as LEADER (👑)
- NO split-brain! ✅

**Why?**
- node-1 sees node-2 has higher term (term 2 > term 1)
- node-1 accepts node-2's leadership
- Both nodes synchronized via Raft log

---

## Step 7: Inspect Raft State (Optional)

**Via API:**

```bash
# Get Raft state
curl http://localhost:8001/api/raft/state | jq

# View replicated log
curl http://localhost:8001/api/raft/log | jq

# Get cluster state
curl http://localhost:8001/api/cluster/state | jq
```

**Or use the interactive script:**

```bash
./test_consensus.sh
```

---

## What You Just Saw

✅ **Leader Election** - Automatic, no manual intervention
✅ **Automatic Failover** - System recovers in ~5 seconds
✅ **Split-Brain Prevention** - Only one leader at a time (guaranteed)
✅ **Log Replication** - All operations logged and replicated
✅ **Fault Tolerance** - System survives leader crashes

---

## Troubleshooting

**No leader elected?**
```bash
# Check logs
docker logs kv-store-node-1
docker logs kv-store-node-2

# Restart containers
docker-compose restart
```

**Dashboard not updating?**
- Refresh page (F5)
- Check browser console for errors
- Verify WebSocket connection (green status in header)

**Writes fail?**
- Check which node is LEADER in dashboard
- Only leader accepts writes
- Wait for leader election to complete (5 seconds)

---

## Next Steps

**Want to learn more?**
- Read [PHASE3_CONSENSUS.md](./PHASE3_CONSENSUS.md) for detailed explanation
- Read [PHASE3_SUMMARY.md](./PHASE3_SUMMARY.md) for implementation details

**Want to experiment?**
- Try killing nodes repeatedly
- Write multiple keys and watch log grow
- Compare Raft log on both nodes (should match!)

**Ready for next phase?**
- Phase 4: Sharding (data partitioning)
- Phase 5: Multi-tenancy (isolation)

---

## Clean Up

```bash
# Stop all containers
docker-compose --profile with-node-2 down

# Remove volumes (optional - clears data)
docker-compose --profile with-node-2 down -v
```

---

**Congratulations!** 🎉

You just witnessed automatic leader election and failover using the Raft consensus algorithm!

This is the same technology powering:
- **etcd** (Kubernetes coordination)
- **Consul** (service discovery)
- **CockroachDB** (distributed SQL)
- **TiKV** (distributed KV store)
