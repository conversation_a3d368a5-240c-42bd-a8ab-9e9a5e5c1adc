FROM python:3.11-slim

WORKDIR /app

# Install simple HTTP server
RUN pip install --no-cache-dir aiohttp

# Copy dashboard HTML
COPY dashboard.html /app/index.html

# Simple Python server to serve the dashboard
RUN echo 'from aiohttp import web\n\
import os\n\
\n\
async def handle(request):\n\
    with open("/app/index.html", "r") as f:\n\
        return web.Response(text=f.read(), content_type="text/html")\n\
\n\
app = web.Application()\n\
app.router.add_get("/", handle)\n\
\n\
if __name__ == "__main__":\n\
    web.run_app(app, host="0.0.0.0", port=3000)\n\
' > /app/server.py

EXPOSE 3000

CMD ["python", "/app/server.py"]
