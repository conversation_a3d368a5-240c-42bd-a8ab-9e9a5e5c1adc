# Week 7 Demo Script - Distributed KV Store

## Setup (Before Class)

```bash
cd week-7/demo/distributed-kv-store

# Start with monolith only
docker-compose up --build -d
```

Wait 10 seconds for services to start.

---

## Phase 0: The Monolith (10 minutes)

### 1. Open Dashboard
- Navigate to: http://localhost:3000
- Show: 1 node active, 0 features enabled

### 2. Demonstrate Basic Operations

**Write data:**
```bash
curl -X PUT http://localhost:8001/api/kv/user:1 \
  -H "Content-Type: application/json" \
  -d '{"key":"user:1","value":"Alice"}'
```

**Read data:**
```bash
curl http://localhost:8001/api/kv/user:1
```

**Dashboard shows:** 1 key on node-1

### 3. Demonstrate Single Point of Failure

**Kill the node:**
```bash
docker stop kv-store-node-1
```

**Try to read:**
```bash
curl http://localhost:8001/api/kv/user:1
# FAILS - connection refused
```

**Teaching moment:** "This is why we need distributed systems!"

**Restart node:**
```bash
docker start kv-store-node-1
sleep 5

# Data is LOST (in-memory only)
curl http://localhost:8001/api/kv/user:1
# Returns 404
```

---

## Phase 1: Add Second Node - The Problem! (15 minutes)

### 1. Enable Second Node Feature

**Via Dashboard:**
- Click "Second Node" toggle → turns ON
- Warning appears: "⚠️ Problem Demonstrated!"

**Or via API:**
```bash
curl -X POST http://localhost:8001/api/features/second_node \
  -H "Content-Type: application/json" \
  -d '{"enabled":true}'
```

### 2. Start Node-2

```bash
docker-compose --profile with-node-2 up -d
```

**Dashboard shows:** 2 nodes active

### 3. Demonstrate the Synchronization Problem

**Test Scenario 1: Write to Node-1, Read from Node-2**

```bash
# 1. Write to node-1
curl -X PUT http://localhost:8001/api/kv/product:100 \
  -H "Content-Type: application/json" \
  -d '{"key":"product:100","value":"iPhone"}'

# Response includes warning:
# "warning": "Data only stored on current node!"

# 2. Read from node-1 - WORKS ✅
curl http://localhost:8001/api/kv/product:100
# Returns: {"key": "product:100", "value": "iPhone", ...}

# 3. Read from node-2 - FAILS ❌
curl http://localhost:8002/api/kv/product:100
# Returns: 404 Not Found!
```

**Ask students:** "Why doesn't node-2 have the data?"

**Test Scenario 2: Reverse (Write to Node-2, Read from Node-1)**

```bash
# Write to node-2
curl -X PUT http://localhost:8002/api/kv/product:200 \
  -H "Content-Type: application/json" \
  -d '{"key":"product:200","value":"MacBook"}'

# Read from node-2 - WORKS ✅
curl http://localhost:8002/api/kv/product:200

# Read from node-1 - FAILS ❌
curl http://localhost:8001/api/kv/product:200
```

### 4. Show Different Data on Each Node

```bash
# Node-1 has:
curl http://localhost:8001/api/kv
# {"keys": ["product:100"], "total": 1}

# Node-2 has:
curl http://localhost:8002/api/kv
# {"keys": ["product:200"], "total": 1}
```

**Teaching moment:** "Just adding nodes doesn't solve the problem - we need REPLICATION!"

---

## Interactive Dashboard Demo (10 minutes)

### 1. Use Dashboard Test Panel

- Select "Test on Node 1"
- PUT key: `user:1`, value: `Alice`
- Result: ✅ Success

- Switch to "Test on Node 2"
- GET key: `user:1`
- Result: ❌ Key not found on node-2!

### 2. Show Cluster State

- Dashboard displays both nodes
- Each node has different data
- Warning box shows: "Second node is active but NOT synchronized"

---

## Discussion Questions (10 minutes)

1. **Q:** What happens if node-1 crashes now?
   **A:** We still lose data written to node-1. Node-2 has different data.

2. **Q:** How many nodes can fail before we lose data?
   **A:** ZERO! Each node has unique data, any failure = data loss.

3. **Q:** What do we need to fix this?
   **A:** Replication - synchronize data across nodes.

4. **Q:** What other problems might we encounter?
   - Network partitions (split brain)
   - Which node is authoritative?
   - How to handle conflicting writes?
   - How to distribute data for scalability?

---

## Preview: Next Phases (5 minutes)

### Phase 2: Replication (Coming Next)
```bash
# Toggle replication ON
curl -X POST http://localhost:8001/api/features/replication \
  -d '{"enabled":true}'

# Now writes to node-1 will sync to node-2!
```

### Phase 3: Consensus (Raft)
- Leader election
- Automatic failover
- Prevents split-brain

### Phase 4: Sharding
- Distribute data across nodes
- Consistent hashing
- Scalability

### Phase 5: Multi-Tenancy
- Namespace isolation
- Per-tenant quotas
- Resource limits

---

## Cleanup

```bash
# Stop all containers
docker-compose --profile with-node-2 down

# Or keep running for students to experiment
```

---

## Troubleshooting

**Dashboard not loading:**
```bash
docker logs kv-store-dashboard
```

**Node not responding:**
```bash
docker logs kv-store-node-1
docker logs kv-store-node-2
```

**Reset everything:**
```bash
docker-compose --profile with-node-2 down -v
docker-compose up --build -d
```

---

## Key Takeaways for Students

✅ **Understood:**
- Why distributed systems are needed (single point of failure)
- The fundamental problem: data synchronization
- Why "just add more servers" doesn't work
- The need for replication, consensus, and coordination

✅ **Demonstrated:**
- Feature toggles to show incremental complexity
- Real-time visualization of cluster state
- Hands-on testing of broken vs working states

✅ **Next Steps:**
- Phase 2 will fix synchronization with replication
- Later phases add fault tolerance, scalability, isolation

---

## Quick Reference Commands

```bash
# View all keys on node-1
curl http://localhost:8001/api/kv

# View all keys on node-2
curl http://localhost:8002/api/kv

# Check feature flags
curl http://localhost:8001/api/features

# Get cluster state2
curl http://localhost:8001/api/cluster/state

# Container status
docker ps --filter "name=kv-store"

# View logs
docker-compose logs -f node-1
docker-compose logs -f node-2
```
