# Phase 4: Sharding and Partitioning

## Overview

Sharding distributes data across multiple nodes to enable horizontal scaling. Instead of replicating the same data to all nodes, sharding partitions the dataset so each node stores a subset.

## Concepts Demonstrated

### 1. **Data Partitioning**
- Split dataset across nodes
- Each shard owns a portion of the key space
- Enables horizontal scaling (add nodes = add capacity)

### 2. **Sharding Strategies**

#### Hash-based (Modulo)
```
shard = hash(key) % num_nodes
```
**Pros:**
- Simple implementation
- Even distribution
- Deterministic routing

**Cons:**
- Rebalancing requires moving ~all data when cluster size changes
- Not resilient to topology changes

**Example:**
```bash
user:1 → hash("user:1") % 2 = 1 → node-2
user:2 → hash("user:2") % 2 = 0 → node-1
```

#### Consistent Hashing
```
Virtual nodes on hash ring
Minimal rebalancing when nodes added/removed
```
**Pros:**
- Only K/n keys move when adding node
- Better distribution with virtual nodes (150 per physical node)
- Resilient to cluster changes

**Cons:**
- More complex implementation
- Slightly more overhead

**Real-world usage:** Cassandra, DynamoDB, Riak

#### Range-based Partitioning
```
a-m → node-1
n-z → node-2
```
**Pros:**
- Good for range queries
- Ordered data access
- Efficient for sequential scans

**Cons:**
- Risk of hotspots (uneven distribution)
- Manual range definition required
- Difficult to rebalance

**Real-world usage:** Google Spanner, CockroachDB (with automatic splitting)

## Architecture

### Request Flow with Sharding

```
Client → Any Node → Shard Router → Target Shard
                    ↓
              Determine shard for key
                    ↓
          Local? → Process immediately
          Remote? → Proxy to target node
```

### Code Structure

1. **sharding.py** - Shard routing logic
   - `ShardRouter` class
   - Three strategies: hash_modulo, consistent_hash, range
   - Virtual nodes for consistent hashing

2. **app.py** - Integration with KV store
   - GET/PUT check shard ownership
   - Automatic proxying to correct shard
   - Sharding API endpoints

3. **config.py** - Feature flags
   - `sharding_enabled`: Toggle feature
   - `sharding_strategy`: Choose strategy

## API Endpoints

### Enable Sharding
```bash
curl -X POST http://localhost:8001/api/features/sharding \
  -H "Content-Type: application/json" \
  -d '{"enabled": true, "strategy": "hash_modulo"}'
```

**Strategies:** `hash_modulo`, `consistent_hash`, `range`

### Check Sharding State
```bash
curl http://localhost:8001/api/sharding/state
```

Response:
```json
{
  "enabled": true,
  "strategy": "hash_modulo",
  "total_nodes": 2,
  "nodes": ["node-1", "node-2"],
  "virtual_nodes": 0
}
```

### Determine Key's Shard
```bash
curl http://localhost:8001/api/sharding/key/user:1
```

Response:
```json
{
  "key": "user:1",
  "shard": "node-1",
  "strategy": "hash_modulo",
  "current_node": "node-1",
  "is_local": true
}
```

## Testing

### Quick Test
```bash
# Enable sharding
curl -X POST http://localhost:8001/api/features/second_node \
  -H "Content-Type: application/json" -d '{"enabled": true}'

curl -X POST http://localhost:8001/api/features/sharding \
  -H "Content-Type: application/json" \
  -d '{"enabled": true, "strategy": "hash_modulo"}'

# Enable on node-2
curl -X POST http://localhost:8002/api/features/second_node \
  -H "Content-Type: application/json" -d '{"enabled": true}'

curl -X POST http://localhost:8002/api/features/sharding \
  -H "Content-Type: application/json" \
  -d '{"enabled": true, "strategy": "hash_modulo"}'

# Write data
curl -X PUT http://localhost:8001/api/kv/user:1 \
  -H "Content-Type: application/json" \
  -d '{"key":"user:1","value":"Alice"}'

curl -X PUT http://localhost:8001/api/kv/user:2 \
  -H "Content-Type: application/json" \
  -d '{"key":"user:2","value":"Bob"}'

# Check distribution
curl http://localhost:8001/api/node/stats | jq '.keys'
curl http://localhost:8002/api/node/stats | jq '.keys'
```

### Comprehensive Test
```bash
./test_sharding.sh
```

This script tests:
- Hash-modulo sharding
- Consistent hashing with virtual nodes
- Range-based partitioning
- Cross-shard proxying
- Key routing

## Data Distribution Examples

### Hash-Modulo (2 nodes)
```
user:1  → node-1
user:2  → node-2
user:3  → node-2
user:4  → node-1
user:5  → node-2
user:6  → node-1
user:7  → node-2
user:8  → node-1
user:9  → node-2
user:10 → node-1
```

**Distribution:** 50/50 split

### Consistent Hash (2 nodes, 300 virtual nodes)
```
product:1  → node-2
product:2  → node-1
product:3  → node-1
product:4  → node-1
product:5  → node-2
...
```

**Distribution:** ~50/50 (with slight variance due to hash collisions)

### Range-based (a-m vs n-z)
```
apple    → node-1 (a-m)
banana   → node-1
cherry   → node-1
...
nut      → node-2 (n-z)
orange   → node-2
pear     → node-2
zebra    → node-2
```

**Distribution:** Depends on key distribution in alphabet

## Sharding vs Replication

| Aspect | Sharding | Replication |
|--------|----------|-------------|
| **Data per node** | Subset | Full copy |
| **Purpose** | Increase capacity | Increase availability |
| **Reads** | Only from owner | From any replica |
| **Writes** | Only to owner | To leader, then replicas |
| **Capacity** | Horizontal scaling | No capacity increase |
| **Fault tolerance** | Data lost if shard dies | No data loss if replica exists |

### Can you use both?

**YES!** Common pattern: **Sharding + Replication**

```
Shard 1:  [Replica A, Replica B, Replica C]
Shard 2:  [Replica A, Replica B, Replica C]
Shard 3:  [Replica A, Replica B, Replica C]
```

- Shard for capacity (partition data)
- Replicate for availability (copy each shard)

**Example:** Cassandra, MongoDB

## Sharding + Consensus (Advanced)

**Architectural Note:** In this demo, sharding and consensus are designed as **alternative strategies**:

- **Consensus (Raft)**: Single leader across all nodes, all nodes have full dataset
- **Sharding**: Data partitioned, each node has subset

In production systems, you can combine them:
- **MongoDB**: Shard → each shard is a replica set with consensus
- **CockroachDB**: Range shards with Raft consensus per range

**Implementation complexity:** Significantly higher (out of scope for this demo)

## Teaching Points

### 1. Horizontal Scaling
- Add more nodes = add more capacity
- Linear scalability (2x nodes = 2x capacity)
- Contrast with vertical scaling (bigger machine)

### 2. Tradeoffs
- **Hash-based:** Simple but inflexible
- **Consistent hash:** Complex but resilient
- **Range-based:** Good for range queries, risk of hotspots

### 3. Rebalancing
- What happens when you add node-3?
- Hash-modulo: ~67% of keys move
- Consistent hash: ~33% of keys move
- Important for production systems

### 4. Query Routing
- How does client know which shard?
- **Smart client:** Client knows sharding logic
- **Proxy:** Any node proxies to correct shard (this demo)
- **Coordinator:** Dedicated routing service

## Real-World Examples

### Cassandra
- Consistent hashing with virtual nodes
- Replication factor × shards = total replicas
- Tunable consistency (how many replicas to read/write)

### MongoDB
- Range-based sharding (chunk-based)
- Each shard is a replica set
- `mongos` router directs queries

### Redis Cluster
- Hash slots (16384 total)
- Consistent hashing variant
- Client-side routing

### CockroachDB
- Range-based sharding with automatic splitting
- Raft consensus per range
- Transactional across shards

## Limitations

### Data Locality
- Cross-shard queries require coordination
- Joins across shards are expensive
- Transactions across shards are complex

### Hot Shards
- Popular keys create hotspots
- Celebrity problem (e.g., @justinbieber's tweets)
- Solution: Further partition hot shards

### Rebalancing
- Moving data is slow
- Can impact performance
- Requires coordination

## Next Steps

1. **Test different strategies:**
   - Compare hash_modulo vs consistent_hash distribution
   - Observe range-based hotspots

2. **Understand rebalancing:**
   - What if you add node-3?
   - How many keys move?

3. **Combine with other features:**
   - Sharding works with basic replication (Phase 2)
   - Incompatible with Raft consensus (by design in this demo)

4. **Production considerations:**
   - How to handle shard failures?
   - How to rebalance with minimal disruption?
   - How to query across shards?

## Summary

**Sharding = Horizontal Partitioning**

- Split data across nodes for **capacity**
- Three strategies: hash-modulo, consistent hash, range
- Automatic request routing/proxying
- Works independently or with replication

**Key Insight:** Sharding is about **scaling capacity**, while replication is about **scaling availability**.

---

**Dashboard:** http://localhost:3000
**Node-1 API:** http://localhost:8001
**Node-2 API:** http://localhost:8002

**Test:** `./test_sharding.sh`
