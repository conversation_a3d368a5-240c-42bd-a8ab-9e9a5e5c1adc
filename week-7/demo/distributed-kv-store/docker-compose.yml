version: '3.8'

services:
  # Node 1 - Always running (primary node)
  node-1:
    build: .
    container_name: kv-store-node-1
    ports:
      - "8001:8000"
    environment:
      - NODE_ID=node-1
      - PORT=8000
    networks:
      - kv-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Node 2 - Starts when second_node_enabled feature flag is toggled
  # Teaching note: This node runs independently - NO data sync!
  node-2:
    build: .
    container_name: kv-store-node-2
    ports:
      - "8002:8000"
    environment:
      - NODE_ID=node-2
      - PORT=8000
    networks:
      - kv-network
    profiles:
      - with-node-2  # Only starts when explicitly enabled
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 10s
      timeout: 5s
      retries: 3

  # Dashboard - Simple web UI to visualize cluster state
  dashboard:
    build:
      context: .
      dockerfile: Dockerfile.dashboard
    container_name: kv-store-dashboard
    ports:
      - "3000:3000"
    networks:
      - kv-network
    depends_on:
      - node-1

networks:
  kv-network:
    driver: bridge
