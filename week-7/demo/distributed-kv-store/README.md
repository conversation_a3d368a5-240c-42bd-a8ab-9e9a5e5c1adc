# Distributed Key-Value Store Demo

## Overview
Educational demo that teaches distributed systems concepts through feature toggles. Start with a monolith and progressively add distributed features one at a time.

## Quick Start

### Phase 0: Monolith (Single Node)

1. **Start node-1 only:**
```bash
docker-compose up --build
```

2. **Open dashboard:**
```
http://localhost:3000
```

3. **Test the monolith:**
- Use the dashboard to PUT/GET values
- Everything works on single node
- **Demonstrate the problem:** Kill the container → data is lost!

### Phase 1: Add Second Node (Broken State) 🎯

This phase demonstrates WHY we need distributed systems features!

1. **Enable second node in dashboard:**
- Click "Second Node" toggle (turns it ON)
- System notifies you to start node-2

2. **Start node-2:**
```bash
docker-compose --profile with-node-2 up -d
```

3. **Demonstrate the problem:**
- Dashboard → Test Panel
- Select "Node 1", PUT key="user:1", value="Alice" ✅
- Select "Node 2", GET key="user:1" → ❌ **404 Not Found!**
- **Teaching moment:** Two nodes exist but data is NOT synchronized!

4. **Why this happens:**
- Each node has independent in-memory storage
- No replication mechanism
- No coordination between nodes
- This is the fundamental problem distributed systems solve!

## API Endpoints

### Key-Value Operations

**PUT a value:**
```bash
curl -X PUT http://localhost:8001/api/kv/mykey \
  -H "Content-Type: application/json" \
  -d '{"key": "mykey", "value": "myvalue"}'
```

**GET a value:**
```bash
curl http://localhost:8001/api/kv/mykey
```

**List all keys:**
```bash
curl http://localhost:8001/api/kv
```

### Feature Toggle API

**Get current features:**
```bash
curl http://localhost:8001/api/features
```

**Toggle second node:**
```bash
curl -X POST http://localhost:8001/api/features/second_node \
  -H "Content-Type: application/json" \
  -d '{"enabled": true}'
```

**Toggle replication (Phase 2 - not yet implemented):**
```bash
curl -X POST http://localhost:8001/api/features/replication \
  -H "Content-Type: application/json" \
  -d '{"enabled": true}'
```

## Accessing Nodes Directly

- **Node 1 (Primary):** http://localhost:8001
- **Node 2 (Secondary):** http://localhost:8002 (only when started with profile)
- **Dashboard:** http://localhost:3000

## Demo Script for Teaching

### Introduction (5 min)
1. Explain the goal: build distributed KV store from scratch
2. Show simple PUT/GET on single node
3. Ask: "What happens if this node crashes?"

### Phase 0: Demonstrate Single Point of Failure (5 min)
```bash
# Write some data
curl -X PUT http://localhost:8001/api/kv/user:1 -d '{"key":"user:1","value":"Alice"}'

# Read it back - works!
curl http://localhost:8001/api/kv/user:1

# Kill the node
docker-compose down

# Try to read - FAILS! Data is gone!
curl http://localhost:8001/api/kv/user:1
```

**Teaching point:** Single node = single point of failure

### Phase 1: Add Second Node - Show the Problem! (10 min)

1. **Start both nodes:**
```bash
docker-compose --profile with-node-2 up -d
```

2. **Write to node-1:**
```bash
curl -X PUT http://localhost:8001/api/kv/user:1 -d '{"key":"user:1","value":"Alice"}'
```

3. **Read from node-1 - WORKS:**
```bash
curl http://localhost:8001/api/kv/user:1
# Response: {"key": "user:1", "value": "Alice", ...}
```

4. **Read from node-2 - FAILS:**
```bash
curl http://localhost:8002/api/kv/user:1
# Response: 404 Not Found!
```

5. **Ask students:** "Why doesn't node-2 have the data?"

**Teaching point:** Just adding nodes doesn't solve the problem - we need replication!

### Phase 2: Preview (Coming Next) (2 min)
- "Next, we'll add replication to sync data across nodes"
- "Then consensus to handle failures"
- "Then sharding to distribute data"
- Show the roadmap in strategy document

## Architecture

```
┌─────────────────────────────────────────────────┐
│                  Dashboard (Port 3000)          │
│         WebSocket for real-time updates         │
└─────────────────┬───────────────────────────────┘
                  │
        ┌─────────┴─────────┐
        │                   │
┌───────▼────────┐  ┌───────▼────────┐
│   Node 1       │  │   Node 2       │
│  (Port 8001)   │  │  (Port 8002)   │
│  PRIMARY       │  │  SECONDARY     │
│                │  │                │
│  ┌──────────┐  │  │  ┌──────────┐  │
│  │ KV Store │  │  │  │ KV Store │  │
│  │ {a: 1}   │  │  │  │ {b: 2}   │  │ <- Different data!
│  └──────────┘  │  │  └──────────┘  │
└────────────────┘  └────────────────┘
        ↑                   ↑
        └───────────────────┘
         NO SYNC (Phase 1 - demonstrates problem)
```

## Troubleshooting

**Can't connect to node-2:**
- Make sure you started it: `docker-compose --profile with-node-2 up -d`
- Check it's running: `docker ps`

**Dashboard not updating:**
- Check WebSocket connection in browser console
- Restart node-1: `docker-compose restart node-1`

**CORS errors:**
- This is expected when testing cross-origin
- Use dashboard instead of direct curl for cross-node testing

## Implementation Status

### Phase 0: Monolith ✅
- Single node KV store
- In-memory storage
- Basic PUT/GET/DELETE operations

### Phase 1: Second Node ✅
- Two independent nodes
- Demonstrates synchronization problem
- No data replication

### Phase 2: Replication ✅
- Leader-follower pattern
- Sync and async replication modes
- Data consistency across nodes
- [See PHASE2_REPLICATION.md](./PHASE2_REPLICATION.md)

### Phase 3: Consensus (Raft) ✅
- Leader election with randomized timeouts
- Automatic failover on leader failure
- Log-based replication
- Split-brain prevention
- [See PHASE3_CONSENSUS.md](./PHASE3_CONSENSUS.md)

### Phase 4: Sharding (Future)
- [ ] Consistent hashing
- [ ] Data partitioning
- [ ] Rebalancing

### Phase 5: Multi-tenancy (Future)
- [ ] Namespace isolation
- [ ] Per-tenant quotas
- [ ] Resource management

## Learning Objectives

After this demo, students will understand:
- ✅ Why distributed systems are needed (single point of failure)
- ✅ The fundamental challenge: data synchronization
- ✅ Why "just add more servers" doesn't work
- ✅ The problems that replication, consensus, and sharding solve

## References
- [Strategy Document](./distributed-kv-store-strategy.md)
- [Week 7 Syllabus](../week-7.md)
