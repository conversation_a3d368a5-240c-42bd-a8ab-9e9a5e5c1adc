#!/bin/bash

# =============================================================================
# PHASE 3: Raft Consensus - Demo Test Script
# =============================================================================
# This script demonstrates the Raft consensus implementation by:
# 1. Enabling consensus
# 2. Triggering leader election
# 3. Testing write operations
# 4. Checking Raft state
# =============================================================================

set -e  # Exit on error

BASE_URL="http://localhost:8001"
NODE2_URL="http://localhost:8002"

echo "============================================"
echo "Phase 3: Raft Consensus Demo"
echo "============================================"
echo ""

# Helper functions
check_health() {
    echo "Checking node health..."
    curl -s "$BASE_URL/health" | jq '.'
    echo ""
}

get_features() {
    echo "Current feature flags:"
    curl -s "$BASE_URL/api/features" | jq '.features'
    echo ""
}

enable_second_node() {
    echo "📍 Step 1: Enabling second node..."
    curl -s -X POST "$BASE_URL/api/features/second_node" \
        -H "Content-Type: application/json" \
        -d '{"enabled": true}' | jq '.'
    echo ""
    echo "⚠️  Remember to start node-2:"
    echo "   docker-compose --profile with-node-2 up -d"
    echo ""
    read -p "Press Enter when node-2 is running..."
}

enable_consensus() {
    echo "📍 Step 2: Enabling Raft consensus..."
    curl -s -X POST "$BASE_URL/api/features/consensus" \
        -H "Content-Type: application/json" \
        -d '{"enabled": true}' | jq '.'
    echo ""
}

wait_for_leader_election() {
    echo "📍 Step 3: Waiting for leader election (5 seconds)..."
    echo "Leader election in progress..."
    sleep 2
    echo "  3 seconds..."
    sleep 1
    echo "  2 seconds..."
    sleep 1
    echo "  1 second..."
    sleep 1
    echo "Leader should be elected now!"
    echo ""
}

check_raft_state() {
    echo "📍 Step 4: Checking Raft state on node-1..."
    curl -s "$BASE_URL/api/raft/state" | jq '.'
    echo ""

    echo "Checking Raft state on node-2..."
    curl -s "$NODE2_URL/api/raft/state" | jq '.' || echo "Node-2 not responding (may not be started)"
    echo ""
}

test_write_to_leader() {
    echo "📍 Step 5: Testing write to leader..."
    echo "Writing user:1=Alice to node-1..."
    curl -s -X PUT "$BASE_URL/api/kv/user:1" \
        -H "Content-Type: application/json" \
        -d '{"key": "user:1", "value": "Alice"}' | jq '.'
    echo ""
}

test_read() {
    echo "📍 Step 6: Reading from node-1..."
    curl -s "$BASE_URL/api/kv/user:1" | jq '.'
    echo ""

    echo "Reading from node-2..."
    curl -s "$NODE2_URL/api/kv/user:1" | jq '.' || echo "Node-2 not responding"
    echo ""
}

check_raft_log() {
    echo "📍 Step 7: Checking Raft replicated log..."
    curl -s "$BASE_URL/api/raft/log" | jq '.'
    echo ""
}

get_cluster_state() {
    echo "📍 Step 8: Getting full cluster state..."
    curl -s "$BASE_URL/api/cluster/state" | jq '.'
    echo ""
}

# Main execution
echo "Starting Raft Consensus demo..."
echo ""

# Check if services are running
check_health

# Get current state
get_features

# Enable features if not already enabled
echo "Do you want to enable consensus? (y/n)"
read -r answer
if [ "$answer" = "y" ]; then
    enable_second_node
    enable_consensus
    wait_for_leader_election
fi

# Check Raft state
check_raft_state

# Test operations
echo "Do you want to test write operations? (y/n)"
read -r answer
if [ "$answer" = "y" ]; then
    test_write_to_leader
    sleep 1
    test_read
    check_raft_log
fi

# Show final state
get_cluster_state

echo "============================================"
echo "Demo Complete!"
echo "============================================"
echo ""
echo "Next steps:"
echo "1. Open dashboard: http://localhost:3000"
echo "2. Watch Raft state updates in real-time"
echo "3. Try killing the leader: docker stop kv-store-node-1"
echo "4. Observe automatic failover in dashboard"
echo ""
