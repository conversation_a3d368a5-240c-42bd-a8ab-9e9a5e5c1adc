# Troubleshooting Guide - Distributed KV Store

## Common Issues and Solutions

---

## Issue 1: "No leader elected yet. Cluster is unavailable" ✅ FIXED

### **Symptoms:**
- Dashboard shows "Leader: No leader elected"
- PUT requests return: `{"detail": "No leader elected yet. Cluster is unavailable."}`
- Raft state shows both nodes as FOLLOWER with no leader

### **Root Cause:**
When you toggle consensus in the dashboard, it only enables the feature on **node-1** (the node the dashboard communicates with). Node-2's consensus remains disabled, so it rejects vote requests from node-1, preventing leader election.

### **Why This Happens:**
Each node maintains independent feature flags. The dashboard toggle API call goes to `http://localhost:8001`, which only updates node-1's configuration. Node-2 never receives the enable command.

### **The Fix (Now Implemented):**
The dashboard's `toggleFeature()` function has been updated to automatically sync certain features across both nodes:

```javascript
// Updated dashboard.html
async function toggleFeature(featureName) {
    // 1. Toggle on node-1
    await fetch(`http://localhost:8001/api/features/${featureName}`, ...);

    // 2. If enabling consensus/replication, also toggle on node-2
    if (featureName === 'consensus' && newState) {
        // First enable second_node on node-2
        await fetch(`http://localhost:8002/api/features/second_node`, ...);

        // Then enable consensus on node-2
        await fetch(`http://localhost:8002/api/features/${featureName}`, ...);
    }
}
```

### **Manual Fix (If Needed):**
If you encounter this issue, manually enable features on both nodes:

```bash
# Enable second_node on both
curl -X POST http://localhost:8001/api/features/second_node \
  -H "Content-Type: application/json" -d '{"enabled": true}'

curl -X POST http://localhost:8002/api/features/second_node \
  -H "Content-Type: application/json" -d '{"enabled": true}'

# Enable consensus on both
curl -X POST http://localhost:8001/api/features/consensus \
  -H "Content-Type: application/json" -d '{"enabled": true}'

curl -X POST http://localhost:8002/api/features/consensus \
  -H "Content-Type: application/json" -d '{"enabled": true}'

# Wait 5 seconds for leader election
sleep 5

# Verify leader elected
curl http://localhost:8001/api/raft/state | jq '.state, .leader_id'
```

### **Expected Result After Fix:**
```json
// Node-1
{"state": "leader", "term": 1, "leader_id": "node-1"}

// Node-2
{"state": "follower", "term": 1, "leader_id": "node-1"}
```

---

## Issue 2: CORS Error - "NetworkError when attempting to fetch resource" ✅ FIXED

### **Symptoms:**
- Dashboard loads but PUT/GET operations fail
- Browser console shows: `NetworkError when attempting to fetch resource`
- No data appears when testing

### **Root Cause:**
The dashboard (running on `localhost:3000`) makes cross-origin requests to the API nodes (`localhost:8001`, `localhost:8002`). Without CORS middleware, FastAPI blocks these requests.

### **The Fix (Implemented):**
Added CORS middleware to `app.py`:

```python
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### **Verification:**
```bash
# Test CORS headers
curl -I -X OPTIONS http://localhost:8001/api/kv/test \
  -H "Origin: http://localhost:3000" \
  -H "Access-Control-Request-Method: PUT"

# Should see:
# HTTP/1.1 200 OK
# access-control-allow-origin: http://localhost:3000
# access-control-allow-methods: DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT
```

---

## Issue 3: Feature Toggle Only Affects One Node

### **Symptoms:**
- Toggle consensus in dashboard → only node-1 changes
- Node-2 still shows `"consensus_enabled": false`
- Inconsistent cluster state

### **Solution:**
Use the updated dashboard (v2) which automatically syncs feature flags across both nodes. Or manually enable features on both nodes using the API.

---

## Issue 4: Leader Election Takes Too Long

### **Symptoms:**
- Waiting more than 10 seconds for leader election
- Nodes stuck in CANDIDATE state
- Term number keeps incrementing

### **Possible Causes:**
1. **Network issues between nodes**
   - Check: `docker network ls`
   - Verify nodes are on same network: `docker inspect kv-store-node-1 | grep NetworkMode`

2. **Both nodes trying to vote for themselves**
   - This is normal - election timeout is randomized (3-5s)
   - Give it 5-10 seconds

3. **Node-2 not running**
   - Check: `docker ps | grep kv-store`
   - Start: `docker-compose --profile with-node-2 up -d`

### **Solution:**
```bash
# Restart cluster for clean state
docker-compose --profile with-node-2 down
docker-compose --profile with-node-2 up -d

# Wait 10 seconds
sleep 10

# Enable features on both nodes (dashboard does this automatically now)
# Check leader elected
curl http://localhost:8001/api/raft/state | jq '.leader_id'
```

---

## Issue 5: "Not the leader" Error on PUT

### **Symptoms:**
```json
{
  "detail": "Not the leader. Current leader: node-1. Please redirect to leader."
}
```

### **Root Cause:**
You're trying to write to a FOLLOWER node. Raft requires all writes go to the LEADER.

### **Solution:**
1. **Check which node is the leader:**
   ```bash
   curl http://localhost:8001/api/raft/state | jq '.leader_id'
   # Output: "node-1"
   ```

2. **Send writes to the leader:**
   - In dashboard: Select the leader node from dropdown
   - Via API: Use the leader's port (usually 8001 for node-1)

### **Expected Behavior:**
- ✅ PUT to leader → Success
- ❌ PUT to follower → Redirect error

---

## Issue 6: Raft Log Not Replicating

### **Symptoms:**
- Leader has log entries, follower doesn't
- `GET /api/raft/log` shows different logs on each node

### **Debugging:**
```bash
# Check leader's log
curl http://localhost:8001/api/raft/log | jq '.log_size, .commit_index'

# Check follower's log
curl http://localhost:8002/api/raft/log | jq '.log_size, .commit_index'

# Check logs for replication errors
docker logs kv-store-node-1 | grep -i "append\|replicate"
```

### **Possible Causes:**
1. Heartbeat mechanism not working
2. Network connectivity issues
3. Follower rejecting AppendEntries

### **Solution:**
```bash
# Restart both nodes
docker-compose restart node-1 node-2

# Re-enable consensus
# (Dashboard automatically syncs to both nodes now)
```

---

## Issue 7: Dashboard Not Showing Real-Time Updates

### **Symptoms:**
- Raft state panel shows "Loading..."
- Node badges don't update
- Have to refresh to see changes

### **Debugging:**
1. **Check WebSocket connection:**
   - Dashboard header should show green "Connected" status
   - If red "Disconnected", refresh page

2. **Check browser console:**
   - F12 → Console tab
   - Look for WebSocket errors

3. **Verify node-1 is running:**
   ```bash
   curl http://localhost:8001/health
   ```

### **Solution:**
- Refresh dashboard (F5)
- Check WebSocket endpoint: `ws://localhost:8001/ws`
- Restart node-1 if needed

---

## Quick Diagnostic Commands

### Check Full Cluster State
```bash
echo "=== Node-1 ===" && \
curl -s http://localhost:8001/api/raft/state | jq && \
echo "=== Node-2 ===" && \
curl -s http://localhost:8002/api/raft/state | jq
```

### Check Feature Flags
```bash
echo "=== Node-1 Features ===" && \
curl -s http://localhost:8001/api/features | jq '.features' && \
echo "=== Node-2 Features ===" && \
curl -s http://localhost:8002/api/features | jq '.features'
```

### View Container Logs
```bash
# Node-1 logs
docker logs kv-store-node-1 --tail 50

# Node-2 logs
docker logs kv-store-node-2 --tail 50

# Dashboard logs
docker logs kv-store-dashboard --tail 20
```

### Complete Reset
```bash
# Nuclear option - fresh start
docker-compose --profile with-node-2 down -v
docker-compose --profile with-node-2 up --build -d

# Wait for startup
sleep 5

# Open dashboard
echo "Dashboard: http://localhost:3000"
```

---

## Prevention Tips

### ✅ Best Practices

1. **Always use the dashboard for feature toggles**
   - It now automatically syncs across both nodes
   - Prevents configuration drift

2. **Wait for leader election**
   - After enabling consensus, wait 5-10 seconds
   - Check Raft info panel shows a leader

3. **Write to the leader only**
   - Check which node is leader in dashboard
   - Select that node for PUT operations

4. **Monitor the logs**
   - Use `docker logs` to see what's happening
   - Look for election/replication messages

5. **Start both nodes together**
   - Use: `docker-compose --profile with-node-2 up -d`
   - Don't start node-1 alone if using consensus

---

## Testing Checklist

Before reporting an issue, verify:

- [ ] Both nodes are running (`docker ps`)
- [ ] Both nodes are healthy (`curl http://localhost:8001/health`)
- [ ] Dashboard is accessible (`http://localhost:3000`)
- [ ] WebSocket shows "Connected" (green badge)
- [ ] Feature flags match on both nodes
- [ ] Leader is elected (Raft panel shows leader ID)
- [ ] You're writing to the leader node
- [ ] Browser console shows no errors (F12)

---

## Getting Help

If you're still stuck:

1. **Collect diagnostics:**
   ```bash
   docker ps > diagnostics.txt
   docker logs kv-store-node-1 --tail 100 >> diagnostics.txt
   docker logs kv-store-node-2 --tail 100 >> diagnostics.txt
   curl http://localhost:8001/api/cluster/state >> diagnostics.txt
   ```

2. **Check documentation:**
   - [PHASE3_CONSENSUS.md](./PHASE3_CONSENSUS.md) - Full Raft guide
   - [QUICKSTART_PHASE3.md](./QUICKSTART_PHASE3.md) - Quick demo
   - [README.md](./README.md) - General overview

3. **Review logs for specific errors:**
   - Look for "ERROR" or "WARN" messages
   - Check for network connectivity issues
   - Verify port conflicts

---

**Last Updated:** 2025-10-06
**Status:** All known issues resolved ✅
