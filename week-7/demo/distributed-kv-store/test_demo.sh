#!/bin/bash
# Automated test script for Week 7 Distributed KV Store Demo
# Tests Phase 0 (Monolith) and Phase 1 (Broken State)

set -e  # Exit on error

echo "======================================================"
echo "  Week 7 Demo - Distributed KV Store Test Suite"
echo "======================================================"
echo ""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Helper function to test HTTP endpoint
test_endpoint() {
    local description=$1
    local url=$2
    local expected_status=$3

    echo -n "Testing: $description ... "

    status_code=$(curl -s -o /dev/null -w "%{http_code}" "$url")

    if [ "$status_code" == "$expected_status" ]; then
        echo -e "${GREEN}✓ PASS${NC} (HTTP $status_code)"
        ((TESTS_PASSED++))
        return 0
    else
        echo -e "${RED}✗ FAIL${NC} (Expected $expected_status, got $status_code)"
        ((TESTS_FAILED++))
        return 1
    fi
}

# Helper function to test JSON response
test_json_field() {
    local description=$1
    local url=$2
    local jq_filter=$3
    local expected_value=$4

    echo -n "Testing: $description ... "

    actual_value=$(curl -s "$url" | python3 -c "import sys,json; data=json.load(sys.stdin); print($jq_filter)" 2>/dev/null || echo "ERROR")

    if [ "$actual_value" == "$expected_value" ]; then
        echo -e "${GREEN}✓ PASS${NC} (Got: $actual_value)"
        ((TESTS_PASSED++))
        return 0
    else
        echo -e "${RED}✗ FAIL${NC} (Expected: $expected_value, Got: $actual_value)"
        ((TESTS_FAILED++))
        return 1
    fi
}

echo "======================================================"
echo "  Phase 0: Monolith Tests"
echo "======================================================"
echo ""

# Test 1: Node-1 is running
test_endpoint "Node-1 health check" "http://localhost:8001/health" "200"

# Test 2: Dashboard is accessible
test_endpoint "Dashboard is accessible" "http://localhost:3000" "200"

# Test 3: Feature flags default state
test_json_field "Second node disabled by default" \
    "http://localhost:8001/api/features" \
    "data['features']['second_node_enabled']" \
    "False"

# Test 4: PUT operation on node-1
echo -n "Testing: PUT operation on node-1 ... "
curl -s -X PUT http://localhost:8001/api/kv/test:1 \
    -H "Content-Type: application/json" \
    -d '{"key":"test:1","value":"testvalue"}' > /dev/null
echo -e "${GREEN}✓ PASS${NC}"
((TESTS_PASSED++))

# Test 5: GET operation on node-1
test_json_field "GET operation on node-1" \
    "http://localhost:8001/api/kv/test:1" \
    "data['value']" \
    "testvalue"

# Test 6: List keys on node-1
test_json_field "List keys on node-1" \
    "http://localhost:8001/api/kv" \
    "'test:1' in data['keys']" \
    "True"

echo ""
echo "======================================================"
echo "  Phase 1: Broken State Tests"
echo "======================================================"
echo ""

# Test 7: Enable second node feature
echo -n "Testing: Enable second node feature ... "
curl -s -X POST http://localhost:8001/api/features/second_node \
    -H "Content-Type: application/json" \
    -d '{"enabled":true}' > /dev/null
sleep 2
echo -e "${GREEN}✓ PASS${NC}"
((TESTS_PASSED++))

# Test 8: Verify feature is enabled
test_json_field "Second node feature enabled" \
    "http://localhost:8001/api/features" \
    "data['features']['second_node_enabled']" \
    "True"

# Test 9: Node-2 is running (should be started manually or with profile)
if docker ps | grep -q "kv-store-node-2"; then
    test_endpoint "Node-2 health check" "http://localhost:8002/health" "200"
else
    echo -e "${YELLOW}⚠ SKIPPED${NC}: Node-2 not running (start with: docker-compose --profile with-node-2 up -d)"
fi

# Test 10: Demonstrate broken state - write to node-1, read from node-2
if docker ps | grep -q "kv-store-node-2"; then
    echo -n "Testing: Write to node-1 ... "
    curl -s -X PUT http://localhost:8001/api/kv/broken:test \
        -H "Content-Type: application/json" \
        -d '{"key":"broken:test","value":"only-on-node-1"}' > /dev/null
    echo -e "${GREEN}✓ PASS${NC}"
    ((TESTS_PASSED++))

    # This SHOULD fail (404) - that's the point!
    echo -n "Testing: Read from node-2 (should fail - 404) ... "
    status_code=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:8002/api/kv/broken:test")

    if [ "$status_code" == "404" ]; then
        echo -e "${GREEN}✓ PASS${NC} (Correctly returned 404 - data not synchronized!)"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}✗ FAIL${NC} (Expected 404, got $status_code)"
        ((TESTS_FAILED++))
    fi

    # Test 11: Reverse - write to node-2, read from node-1 should fail
    echo -n "Testing: Write to node-2 ... "
    curl -s -X PUT http://localhost:8002/api/kv/broken:test2 \
        -H "Content-Type: application/json" \
        -d '{"key":"broken:test2","value":"only-on-node-2"}' > /dev/null
    echo -e "${GREEN}✓ PASS${NC}"
    ((TESTS_PASSED++))

    echo -n "Testing: Read from node-1 (should fail - 404) ... "
    status_code=$(curl -s -o /dev/null -w "%{http_code}" "http://localhost:8001/api/kv/broken:test2")

    if [ "$status_code" == "404" ]; then
        echo -e "${GREEN}✓ PASS${NC} (Correctly returned 404 - data not synchronized!)"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}✗ FAIL${NC} (Expected 404, got $status_code)"
        ((TESTS_FAILED++))
    fi
else
    echo -e "${YELLOW}⚠ SKIPPED${NC}: Node-2 tests (start node-2 to test broken state)"
fi

echo ""
echo "======================================================"
echo "  Test Summary"
echo "======================================================"
echo ""
echo -e "Tests Passed: ${GREEN}${TESTS_PASSED}${NC}"
echo -e "Tests Failed: ${RED}${TESTS_FAILED}${NC}"
echo ""

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "${GREEN}✓ All tests passed! Demo is ready.${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Open dashboard: http://localhost:3000"
    echo "2. Follow DEMO_SCRIPT.md for teaching flow"
    echo "3. Start node-2 with: docker-compose --profile with-node-2 up -d"
    exit 0
else
    echo -e "${RED}✗ Some tests failed. Please check the setup.${NC}"
    exit 1
fi
