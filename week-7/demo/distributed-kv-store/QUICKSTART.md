# Quick Start Guide - Distributed KV Store Demo

## 🚀 Get Started in 2 Minutes

### 1. Start the Demo
```bash
cd week-7/demo/distributed-kv-store
docker-compose up --build -d
```

### 2. Open Dashboard
Navigate to: **http://localhost:3000**

### 3. Test Phase 0 (Monolith)
In the dashboard test panel:
- PUT key: `user:1`, value: `Alice` on Node 1
- GET key: `user:1` → ✅ Works!

### 4. Enable Phase 1 (Broken State)
- Click "Second Node" toggle in dashboard
- In terminal: `docker-compose --profile with-node-2 up -d`
- Dashboard now shows 2 nodes

### 5. Demonstrate the Problem
- PUT key: `product:1`, value: `iPhone` on Node 1
- Switch to Node 2 in dashboard
- GET key: `product:1` → ❌ **404 Not Found!**

**🎯 Teaching Moment:** Two nodes exist but data is NOT synchronized!

---

## 📊 What You Built

```
Project Structure:
├── app.py                    # FastAPI application (KV store + feature toggles)
├── kv_store.py              # In-memory key-value store
├── config.py                # Feature flags configuration
├── dashboard.html           # Real-time dashboard UI
├── docker-compose.yml       # Multi-node orchestration
├── Dockerfile               # Node container image
├── Dockerfile.dashboard     # Dashboard container image
├── requirements.txt         # Python dependencies
├── README.md               # Detailed documentation
├── DEMO_SCRIPT.md          # Step-by-step demo script
└── QUICKSTART.md           # This file
```

---

## 🎓 Learning Objectives Achieved

✅ **Phase 0 (Monolith):**
- Single node key-value store
- Demonstrated single point of failure
- Data loss when node crashes

✅ **Phase 1 (Broken State):**
- Added second node WITHOUT synchronization
- Demonstrated why "just adding nodes" doesn't work
- Showed data inconsistency across nodes
- Motivated the need for replication

---

## 🧪 Testing Commands

### Basic Operations
```bash
# Write to node-1
curl -X PUT http://localhost:8001/api/kv/mykey \
  -H "Content-Type: application/json" \
  -d '{"key":"mykey","value":"myvalue"}'

# Read from node-1
curl http://localhost:8001/api/kv/mykey

# Read from node-2 (will fail if written to node-1)
curl http://localhost:8002/api/kv/mykey
```

### Feature Toggles
```bash
# Check current features
curl http://localhost:8001/api/features

# Enable second node
curl -X POST http://localhost:8001/api/features/second_node \
  -d '{"enabled":true}'
```

### Cluster State
```bash
# View all keys on each node
curl http://localhost:8001/api/kv  # Node-1
curl http://localhost:8002/api/kv  # Node-2

# Get cluster state
curl http://localhost:8001/api/cluster/state
```

---

## 🎯 Demo Flow (20 minutes)

| Time | Phase | What to Show |
|------|-------|--------------|
| 0-5 min | Setup | Open dashboard, show 1 node active |
| 5-10 min | Monolith | PUT/GET operations, kill node → data loss |
| 10-15 min | Add Node-2 | Toggle feature, start second node |
| 15-20 min | Broken State | Write to node-1, read from node-2 → 404! |

**Key Message:** "See the problem? We need replication, consensus, and coordination!"

---

## 🔧 Troubleshooting

**Dashboard not loading?**
```bash
docker logs kv-store-dashboard
# Should see: "Running on http://0.0.0.0:3000"
```

**Node not responding?**
```bash
# Check containers are running
docker ps --filter "name=kv-store"

# View logs
docker logs kv-store-node-1
docker logs kv-store-node-2
```

**Reset everything:**
```bash
docker-compose --profile with-node-2 down -v
docker-compose up --build -d
```

---

## 📝 Next Steps

### Phase 2: Add Replication (Coming Soon)
- Implement leader-follower replication
- Sync data across nodes
- Fix the synchronization problem

### Phase 3: Add Consensus (Raft)
- Leader election
- Automatic failover
- Handle network partitions

### Phase 4: Add Sharding
- Consistent hashing
- Data partitioning across nodes
- Scalability

### Phase 5: Add Multi-Tenancy
- Namespace isolation
- Per-tenant quotas
- Resource management

---

## 🎥 Recording the Demo (Backup Option)

If you want to pre-record as backup:

```bash
# 1. Start everything
docker-compose --profile with-node-2 up -d

# 2. Open dashboard in browser: http://localhost:3000

# 3. Use screen recording software (OBS, QuickTime, etc.)

# 4. Follow DEMO_SCRIPT.md step-by-step

# 5. Highlight:
#    - Feature toggle activation
#    - Dashboard real-time updates
#    - The "broken state" 404 errors
#    - Warning messages
```

---

## 📚 Additional Resources

- **Strategy Document:** `distributed-kv-store-strategy.md`
- **Full README:** `README.md`
- **Demo Script:** `DEMO_SCRIPT.md`
- **Week 7 Syllabus:** `../../week-7.md`

---

## ✅ Success Criteria

The demo is working if:
1. ✅ Dashboard loads at http://localhost:3000
2. ✅ Can PUT/GET data on node-1
3. ✅ Can toggle "Second Node" feature
4. ✅ Node-2 starts with profile command
5. ✅ Writing to node-1 → reading from node-2 returns 404
6. ✅ Dashboard shows different data on each node
7. ✅ Warning appears: "Data only stored on current node!"

---

## 🎓 What Students Learn

After this demo, students understand:
- ✅ **The Problem:** Single point of failure in monoliths
- ✅ **The Challenge:** Data synchronization in distributed systems
- ✅ **The Reality:** Just adding nodes doesn't solve the problem
- ✅ **The Solution Path:** Need replication → consensus → sharding → isolation

**Most Important:** Students SEE the problem before learning the solution!

---

## 🙋 FAQ

**Q: Why don't you implement replication in Phase 1?**
A: Pedagogical choice - students need to SEE the problem first, then appreciate the solution.

**Q: Why use feature toggles instead of separate demos?**
A: Shows evolution from monolith → distributed system in one codebase, like real-world refactoring.

**Q: Can I skip Phase 0 and start with Phase 1?**
A: No! Phase 0 demonstrates single point of failure, which motivates Phase 1.

**Q: What if I want to test all features at once?**
A: Don't! The linear flow (one feature at a time) is intentional for teaching.

---

**Ready to teach? Start with:** `docker-compose up --build -d` 🚀
