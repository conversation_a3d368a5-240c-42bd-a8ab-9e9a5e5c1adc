#!/bin/bash

# =============================================================================
# PHASE 4: Sharding - Demo Test Script
# =============================================================================
# This script demonstrates sharding implementation with different strategies
# =============================================================================

set -e  # Exit on error

BASE_URL="http://localhost:8001"
NODE2_URL="http://localhost:8002"

echo "============================================"
echo "Phase 4: Sharding Demo"
echo "============================================"
echo ""

# Reset cluster first
echo "📍 Step 1: Resetting cluster..."
curl -s -X POST "$BASE_URL/api/reset" > /dev/null
curl -s -X POST "$NODE2_URL/api/reset" > /dev/null
echo "Cluster reset complete"
echo ""

# Enable second node
echo "📍 Step 2: Enabling second node..."
curl -s -X POST "$BASE_URL/api/features/second_node" \
    -H "Content-Type: application/json" \
    -d '{"enabled": true}' | jq -r '.message'
echo ""

# Enable sharding with hash_modulo strategy
echo "📍 Step 3: Enabling sharding (hash_modulo strategy)..."
curl -s -X POST "$BASE_URL/api/features/sharding" \
    -H "Content-Type: application/json" \
    -d '{"enabled": true, "strategy": "hash_modulo"}' | jq '.'
echo ""

curl -s -X POST "$NODE2_URL/api/features/second_node" \
    -H "Content-Type: application/json" \
    -d '{"enabled": true}' > /dev/null

curl -s -X POST "$NODE2_URL/api/features/sharding" \
    -H "Content-Type: application/json" \
    -d '{"enabled": true, "strategy": "hash_modulo"}' > /dev/null
echo ""

# Write test data
echo "📍 Step 4: Writing test data (10 keys)..."
for i in {1..10}; do
    curl -s -X PUT "$BASE_URL/api/kv/user:$i" \
        -H "Content-Type: application/json" \
        -d "{\"key\":\"user:$i\",\"value\":\"User${i}\"}" > /dev/null
    echo "  Written: user:$i"
done
echo ""

# Check shard distribution
echo "📍 Step 5: Checking shard distribution..."
echo "Node-1 keys:"
curl -s "$BASE_URL/api/node/stats" | jq '.keys'
echo ""
echo "Node-2 keys:"
curl -s "$NODE2_URL/api/node/stats" | jq '.keys'
echo ""

# Test key routing
echo "📍 Step 6: Testing key routing..."
for i in 1 2 3; do
    echo "Key user:$i belongs to shard:"
    curl -s "$BASE_URL/api/sharding/key/user:$i" | jq '{key, shard, is_local}'
done
echo ""

# Test reading from different nodes (proxying)
echo "📍 Step 7: Testing cross-shard reads (proxying)..."
echo "Reading user:1 from node-1:"
curl -s "$BASE_URL/api/kv/user:1" | jq '{key, value, node_id, proxied_from, shard_owner}'
echo ""
echo "Reading user:1 from node-2 (should proxy):"
curl -s "$NODE2_URL/api/kv/user:1" | jq '{key, value, node_id, proxied_from, shard_owner}'
echo ""

# Test consistent hashing
echo "📍 Step 8: Switching to consistent_hash strategy..."
curl -s -X POST "$BASE_URL/api/features/sharding" \
    -H "Content-Type: application/json" \
    -d '{"enabled": false}' > /dev/null

curl -s -X POST "$NODE2_URL/api/features/sharding" \
    -H "Content-Type: application/json" \
    -d '{"enabled": false}' > /dev/null

# Reset data
curl -s -X POST "$BASE_URL/api/reset" > /dev/null
curl -s -X POST "$NODE2_URL/api/reset" > /dev/null

# Re-enable with consistent hash
curl -s -X POST "$BASE_URL/api/features/second_node" \
    -H "Content-Type: application/json" \
    -d '{"enabled": true}' > /dev/null

curl -s -X POST "$BASE_URL/api/features/sharding" \
    -H "Content-Type: application/json" \
    -d '{"enabled": true, "strategy": "consistent_hash"}' | jq '{message, strategy: .shard_info.strategy, virtual_nodes: .shard_info.virtual_nodes}'

curl -s -X POST "$NODE2_URL/api/features/second_node" \
    -H "Content-Type: application/json" \
    -d '{"enabled": true}' > /dev/null

curl -s -X POST "$NODE2_URL/api/features/sharding" \
    -H "Content-Type: application/json" \
    -d '{"enabled": true, "strategy": "consistent_hash"}' > /dev/null
echo ""

# Write data with consistent hash
echo "📍 Step 9: Writing data with consistent_hash..."
for i in {1..10}; do
    curl -s -X PUT "$BASE_URL/api/kv/product:$i" \
        -H "Content-Type: application/json" \
        -d "{\"key\":\"product:$i\",\"value\":\"Product${i}\"}" > /dev/null
    echo "  Written: product:$i"
done
echo ""

echo "Node-1 keys:"
curl -s "$BASE_URL/api/node/stats" | jq '.keys'
echo ""
echo "Node-2 keys:"
curl -s "$NODE2_URL/api/node/stats" | jq '.keys'
echo ""

# Test range-based sharding
echo "📍 Step 10: Testing range-based sharding..."
curl -s -X POST "$BASE_URL/api/features/sharding" \
    -H "Content-Type: application/json" \
    -d '{"enabled": false}' > /dev/null

curl -s -X POST "$NODE2_URL/api/features/sharding" \
    -H "Content-Type: application/json" \
    -d '{"enabled": false}' > /dev/null

curl -s -X POST "$BASE_URL/api/reset" > /dev/null
curl -s -X POST "$NODE2_URL/api/reset" > /dev/null

curl -s -X POST "$BASE_URL/api/features/second_node" \
    -H "Content-Type: application/json" \
    -d '{"enabled": true}' > /dev/null

curl -s -X POST "$BASE_URL/api/features/sharding" \
    -H "Content-Type: application/json" \
    -d '{"enabled": true, "strategy": "range"}' | jq '{message, strategy: .shard_info.strategy}'

curl -s -X POST "$NODE2_URL/api/features/second_node" \
    -H "Content-Type: application/json" \
    -d '{"enabled": true}' > /dev/null

curl -s -X POST "$NODE2_URL/api/features/sharding" \
    -H "Content-Type: application/json" \
    -d '{"enabled": true, "strategy": "range"}' > /dev/null
echo ""

echo "Writing alphabetically distributed keys..."
for key in apple banana cherry dog elephant fox grape hat ice jacket kite lemon mango nut orange pear queen rose star turtle umbrella violin whale xray yacht zebra; do
    curl -s -X PUT "$BASE_URL/api/kv/$key" \
        -H "Content-Type: application/json" \
        -d "{\"key\":\"$key\",\"value\":\"${key^}\"}" > /dev/null
    echo "  Written: $key"
done
echo ""

echo "Node-1 keys (a-m):"
curl -s "$BASE_URL/api/node/stats" | jq '.keys'
echo ""
echo "Node-2 keys (n-z):"
curl -s "$NODE2_URL/api/node/stats" | jq '.keys'
echo ""

echo "============================================"
echo "Demo Complete!"
echo "============================================"
echo ""
echo "Summary:"
echo "✓ Hash-modulo sharding: Distributes keys evenly using hash % num_nodes"
echo "✓ Consistent hashing: Uses virtual nodes for minimal rebalancing"
echo "✓ Range-based sharding: Partitions alphabetically (a-m vs n-z)"
echo ""
echo "All sharding strategies demonstrated successfully!"
echo ""
