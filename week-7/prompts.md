1. Generate a quiz with 5 questions multiple choice and 1 right answer in markdown format? Is there a way you can upload this to canvas or is ther a format where if I upload a file to canbas it automatically converts to quizk questions?
2/ Generate an assignment based on the topics we have for week 6 and 7. <PERSON><PERSON> can refer other assignments we have generated for prior weeks as an esxample. 
3. Can you add the next topic which is sharding and partitioning to the demo app in week-7 and ensure that it works fine with otjerr features correctly? Please run some simulations nto test the app so it does nolt break.
4. Can you add the last feature which is multi tenancy to the app and make sure that it works fine and test it?
6. Can you generate the markdown file based on demo app to teach all concepts keeping the time for while md to be 1hr. I want compact diagrams in mermaid and illustrations where the md content is very relatable and applicable to the demo app we have built so far?
7. change the theme of different shaes of grey.

Is there a device or app or microphone where I can speak and it makes my voice feminine to the people who hear?
add the following to dista cart or swadessi:
- danvantara gulika
- shankapushpi
- licorice
- shatavari ghritam or gulam or tablets
- 

add the following to amazon:
- pommegranate
- avocado
- kiwi
- fish 

- side table that is urely wood like the shoe table that is small and compact for the small space we have.

download python3 doc and add to zed
download and set offline models

get a new higher level bypass code with uc berkeley - send them an email and g chat now and check again tomorrow to join office hours.