# Week 6-7 Assignment: Distributed E-commerce Analytics Platform

## Overview

Build a distributed e-commerce analytics platform that demonstrates CQRS pattern, stream processing, and distributed systems concepts. You'll extend the distributed key-value store from Week 7 to create a real-time analytics system for an e-commerce platform.

**Duration:** 2-3 weeks  
**Difficulty:** Intermediate to Advanced  
**Prerequisites:** Completion of Week 6-7 materials and distributed KV store demo

---

## Learning Objectives

By completing this assignment, you will:
- ✅ Implement CQRS pattern with separate read/write models
- ✅ Build a stream processing pipeline for real-time analytics
- ✅ Apply distributed systems concepts (consensus, replication, sharding)
- ✅ Handle eventual consistency and data synchronization challenges
- ✅ Create resilient systems with proper error handling

---

## Business Requirements

### E-commerce Platform Context
You're building the backend for "ShopStream" - a high-traffic e-commerce platform that needs:

1. **Real-time Analytics**: Track user behavior, sales metrics, and inventory changes
2. **High Availability**: 99.9% uptime with automatic failover
3. **Scalability**: Handle 10,000+ concurrent users and 1,000 orders/minute
4. **Data Consistency**: Ensure order processing is reliable and auditable
5. **Performance**: Sub-100ms response times for read operations

### Core Features to Implement
- **Order Processing**: Create, update, and track orders
- **Inventory Management**: Real-time stock tracking with reservations
- **User Analytics**: Track user sessions, page views, and purchase behavior
- **Sales Dashboard**: Real-time metrics for business intelligence
- **Notification System**: Event-driven alerts and webhooks

---

## Architecture Overview

```mermaid
graph TD
    subgraph "Command Side (Writes)"
        A[Order Service] --> B[Order DB<br/>PostgreSQL]
        C[Inventory Service] --> D[Inventory DB<br/>PostgreSQL]
        E[User Service] --> F[User DB<br/>PostgreSQL]
    end
    
    subgraph "Event Stream"
        G[Event Bus<br/>Kafka/Redis Streams]
    end
    
    subgraph "Query Side (Reads)"
        H[Analytics Service] --> I[Analytics DB<br/>MongoDB/Elasticsearch]
        J[Dashboard Service] --> K[Dashboard Cache<br/>Redis]
        L[Notification Service] --> M[Notification Queue]
    end
    
    B --> G
    D --> G
    F --> G
    G --> H
    G --> J
    G --> L
    
    N[Web Dashboard] --> J
    O[Mobile App] --> A
    O --> E
```

---

## Part 1: CQRS Implementation (40 points)

### Task 1.1: Command Side - Order Processing Service (15 points)

**Objective**: Create a write-optimized service for order processing.

**Requirements**:
1. **Order Aggregate** with business logic:
   ```python
   class Order:
       def __init__(self, customer_id, items):
           self.id = generate_order_id()
           self.customer_id = customer_id
           self.items = items
           self.status = OrderStatus.PENDING
           self.total_amount = self.calculate_total()
           self.created_at = datetime.utcnow()
       
       def confirm_payment(self, payment_id):
           # Business rule: Can only confirm pending orders
           if self.status != OrderStatus.PENDING:
               raise InvalidOrderStateException()
           self.status = OrderStatus.CONFIRMED
           self.payment_id = payment_id
           # Emit domain event
           DomainEvents.raise(OrderConfirmed(self.id, self.customer_id, self.total_amount))
   ```

2. **Command Handlers**:
   - `CreateOrderCommand` → Creates new order
   - `ConfirmPaymentCommand` → Confirms payment for order
   - `CancelOrderCommand` → Cancels order with business rules

3. **Domain Events**:
   - `OrderCreated`
   - `OrderConfirmed` 
   - `OrderCancelled`
   - `InventoryReserved`

4. **Database Schema** (normalized for consistency):
   ```sql
   CREATE TABLE orders (
       id UUID PRIMARY KEY,
       customer_id UUID NOT NULL,
       status VARCHAR(20) NOT NULL,
       total_amount DECIMAL(10,2) NOT NULL,
       created_at TIMESTAMP NOT NULL,
       updated_at TIMESTAMP NOT NULL
   );
   
   CREATE TABLE order_items (
       id UUID PRIMARY KEY,
       order_id UUID REFERENCES orders(id),
       product_id UUID NOT NULL,
       quantity INTEGER NOT NULL,
       unit_price DECIMAL(10,2) NOT NULL
   );
   ```

**Deliverables**:
- [ ] Order service with command handlers
- [ ] Domain events implementation
- [ ] Unit tests for business logic
- [ ] API endpoints for order operations

### Task 1.2: Query Side - Analytics Service (15 points)

**Objective**: Create read-optimized models for analytics and reporting.

**Requirements**:
1. **Denormalized Read Models**:
   ```python
   # MongoDB document for fast analytics queries
   {
       "_id": "order_123",
       "customer_id": "customer_456",
       "customer_name": "John Doe",
       "customer_tier": "premium",
       "order_date": "2025-01-15T10:30:00Z",
       "total_amount": 299.99,
       "currency": "USD",
       "items": [
           {
               "product_id": "product_789",
               "product_name": "Wireless Headphones",
               "category": "Electronics",
               "quantity": 1,
               "unit_price": 299.99
           }
       ],
       "status": "confirmed",
       "payment_method": "credit_card",
       "shipping_address": {
           "country": "US",
           "state": "CA",
           "city": "San Francisco"
       },
       # Pre-calculated analytics fields
       "is_first_purchase": false,
       "customer_lifetime_value": 1250.00,
       "order_processing_time_ms": 1250
   }
   ```

2. **Analytics Aggregations**:
   - Daily sales summaries
   - Customer behavior metrics
   - Product performance data
   - Geographic sales distribution

3. **Query APIs**:
   - `/api/analytics/sales/daily` - Daily sales metrics
   - `/api/analytics/customers/top` - Top customers by value
   - `/api/analytics/products/trending` - Trending products
   - `/api/analytics/dashboard` - Real-time dashboard data

**Deliverables**:
- [ ] Analytics service with read models
- [ ] MongoDB/Elasticsearch integration
- [ ] Query APIs with proper indexing
- [ ] Performance tests showing <100ms response times

### Task 1.3: Event Projection Handlers (10 points)

**Objective**: Synchronize read models when write models change.

**Requirements**:
1. **Event Handlers** that update read models:
   ```python
   @event_handler
   def handle_order_created(event: OrderCreated):
       # Create analytics record
       analytics_record = {
           "order_id": event.order_id,
           "customer_id": event.customer_id,
           "created_at": event.occurred_at,
           "status": "pending",
           # Enrich with customer data
           "customer_name": customer_service.get_name(event.customer_id),
           "customer_tier": customer_service.get_tier(event.customer_id)
       }
       analytics_db.insert(analytics_record)
   ```

2. **Data Enrichment**: Combine data from multiple services
3. **Error Handling**: Retry logic for failed projections
4. **Monitoring**: Track projection lag and failures

**Deliverables**:
- [ ] Event projection handlers
- [ ] Data enrichment logic
- [ ] Error handling and retry mechanisms
- [ ] Monitoring dashboard for projection health

---

## Part 2: Stream Processing Pipeline (35 points)

### Task 2.1: Event Stream Infrastructure (10 points)

**Objective**: Set up event streaming infrastructure.

**Requirements**:
1. **Event Bus Setup**:
   - Use Redis Streams or Apache Kafka
   - Configure topics: `orders`, `inventory`, `users`, `analytics`
   - Set up consumer groups for parallel processing

2. **Event Schema**:
   ```json
   {
       "event_id": "evt_123456",
       "event_type": "OrderCreated",
       "aggregate_id": "order_789",
       "aggregate_type": "Order",
       "event_data": {
           "order_id": "order_789",
           "customer_id": "customer_456",
           "total_amount": 299.99,
           "items": [...]
       },
       "metadata": {
           "timestamp": "2025-01-15T10:30:00Z",
           "version": 1,
           "correlation_id": "req_abc123"
       }
   }
   ```

3. **Producer/Consumer Setup**:
   - Event producers in command services
   - Event consumers for analytics processing
   - Dead letter queues for failed events

**Deliverables**:
- [ ] Event streaming infrastructure setup
- [ ] Event schema definitions
- [ ] Producer/consumer implementations
- [ ] Integration tests for event flow

### Task 2.2: Real-time Analytics Processing (15 points)

**Objective**: Process events in real-time to generate analytics.

**Requirements**:
1. **Stream Processing Logic**:
   ```python
   # Example: Calculate real-time sales metrics
   def process_order_stream():
       for event in order_stream:
           if event.type == "OrderConfirmed":
               # Update real-time metrics
               update_daily_sales(event.data.total_amount)
               update_product_sales(event.data.items)
               update_customer_metrics(event.data.customer_id)
               
               # Trigger alerts if needed
               if event.data.total_amount > 1000:
                   send_high_value_order_alert(event.data)
   ```

2. **Windowed Aggregations**:
   - Sales per minute/hour/day
   - Customer activity sessions
   - Product view-to-purchase conversion rates

3. **Real-time Alerts**:
   - High-value orders (>$1000)
   - Inventory low stock warnings
   - Unusual customer behavior patterns

**Deliverables**:
- [ ] Stream processing implementation
- [ ] Windowed aggregation logic
- [ ] Real-time alerting system
- [ ] Performance metrics (events/second processed)

### Task 2.3: Event Sourcing for Audit Trail (10 points)

**Objective**: Implement event sourcing for complete audit capability.

**Requirements**:
1. **Event Store**:
   ```sql
   CREATE TABLE event_store (
       event_id UUID PRIMARY KEY,
       aggregate_id UUID NOT NULL,
       aggregate_type VARCHAR(50) NOT NULL,
       event_type VARCHAR(100) NOT NULL,
       event_data JSONB NOT NULL,
       event_version INTEGER NOT NULL,
       occurred_at TIMESTAMP NOT NULL,
       UNIQUE(aggregate_id, event_version)
   );
   ```

2. **Aggregate Reconstruction**:
   - Rebuild order state from events
   - Snapshot pattern for performance
   - Event replay capabilities

3. **Audit Queries**:
   - Complete order history
   - Customer activity timeline
   - System state at any point in time

**Deliverables**:
- [ ] Event store implementation
- [ ] Aggregate reconstruction logic
- [ ] Snapshot mechanism
- [ ] Audit query APIs

---

## Part 3: Distributed Systems Features (25 points)

### Task 3.1: Consensus and Leader Election (10 points)

**Objective**: Extend the distributed KV store to handle analytics coordination.

**Requirements**:
1. **Analytics Coordinator**: Use Raft consensus to elect analytics leader
2. **Distributed Aggregation**: Coordinate metrics across multiple nodes
3. **Failover Handling**: Automatic leader election when coordinator fails

**Integration with KV Store**:
- Extend existing Raft implementation
- Add analytics-specific consensus operations
- Maintain consistency across analytics nodes

**Deliverables**:
- [ ] Analytics coordinator with Raft consensus
- [ ] Distributed aggregation logic
- [ ] Failover testing and validation

### Task 3.2: Data Sharding and Partitioning (10 points)

**Objective**: Implement sharding for analytics data.

**Requirements**:
1. **Sharding Strategy**:
   - Partition by customer_id for user analytics
   - Partition by date for time-series data
   - Partition by product_category for product analytics

2. **Consistent Hashing**: Distribute data evenly across nodes
3. **Rebalancing**: Handle adding/removing analytics nodes

**Deliverables**:
- [ ] Sharding implementation
- [ ] Consistent hashing algorithm
- [ ] Node rebalancing logic

### Task 3.3: Multi-tenancy Support (5 points)

**Objective**: Add tenant isolation to the analytics platform.

**Requirements**:
1. **Tenant Isolation**: Separate analytics data by tenant
2. **Resource Quotas**: Limit resources per tenant
3. **Cross-tenant Analytics**: Aggregate metrics across tenants (admin view)

**Deliverables**:
- [ ] Multi-tenant data isolation
- [ ] Resource quota enforcement
- [ ] Admin analytics dashboard

---

## Bonus Challenges (Extra Credit)

### Bonus 1: Circuit Breaker Pattern (5 points)
Implement circuit breakers for external service calls with fallback mechanisms.

### Bonus 2: Distributed Caching (5 points)
Add Redis cluster for distributed caching of analytics results.

### Bonus 3: Chaos Engineering (5 points)
Create chaos testing scenarios to validate system resilience.

---

## Submission Requirements

### Code Deliverables
1. **Source Code**: Complete implementation with clear documentation
2. **Docker Compose**: Setup for running the entire system locally
3. **API Documentation**: OpenAPI/Swagger specs for all endpoints
4. **Database Migrations**: Scripts for setting up schemas

### Documentation
1. **Architecture Document**: System design and component interactions
2. **Setup Guide**: Step-by-step instructions for running the system
3. **Performance Report**: Benchmarks and optimization analysis
4. **Lessons Learned**: Challenges faced and solutions implemented

### Testing
1. **Unit Tests**: >80% code coverage for business logic
2. **Integration Tests**: End-to-end scenarios
3. **Performance Tests**: Load testing results
4. **Chaos Tests**: Failure scenario validation

---

## Evaluation Criteria

| **Category** | **Weight** | **Criteria** |
|--------------|------------|--------------|
| **Functionality** | 40% | All requirements implemented correctly |
| **Code Quality** | 25% | Clean, maintainable, well-documented code |
| **Architecture** | 20% | Proper application of patterns and principles |
| **Testing** | 10% | Comprehensive test coverage |
| **Documentation** | 5% | Clear, complete documentation |

### Grade Scale
- **A (90-100%)**: Exceptional implementation with bonus features
- **B (80-89%)**: Complete implementation meeting all requirements
- **C (70-79%)**: Most requirements met with minor issues
- **D (60-69%)**: Basic implementation with significant gaps
- **F (<60%)**: Incomplete or non-functional implementation

---

## Getting Started

1. **Fork the Repository**: Start with the Week 7 distributed KV store
2. **Set Up Environment**: Docker, Python/Java, Redis/Kafka
3. **Plan Your Architecture**: Design before coding
4. **Implement Incrementally**: Start with Part 1, then Part 2, then Part 3
5. **Test Continuously**: Write tests as you develop
6. **Document Everything**: Keep documentation up-to-date

**Estimated Timeline**:
- Week 1: Part 1 (CQRS Implementation)
- Week 2: Part 2 (Stream Processing)
- Week 3: Part 3 (Distributed Systems) + Documentation

Good luck building your distributed e-commerce analytics platform! 🚀
