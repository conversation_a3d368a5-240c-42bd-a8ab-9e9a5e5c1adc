# Week 7: Distributed Key-Value Store Demo - Strategy Document

## Overview
Build a distributed key-value store demo that teaches distributed systems concepts through feature toggles. Students see the system evolve from a monolith to a distributed system by enabling features one at a time.

## Core Principle: Linear Feature Addition
- **NO combination testing** - features are added linearly in class
- **Demo flow**: Start with monolith → add one feature → demo → add next feature → demo
- **Feature toggles**: Control plane API to enable/disable features
- **Visual feedback**: Dashboard shows cluster state in real-time

## Technology Stack
```yaml
Backend:
  - Python + FastAPI (async, simple)
  - In-memory dict for KV storage
  - Config-driven feature flags

Infrastructure:
  - Docker Compose (multi-node orchestration)
  - LocalStack (cloud simulation - S3, DynamoDB)

Dashboard:
  - Simple HTML/JS with WebSockets
  - Real-time cluster state visualization
  - Feature toggle controls

Scope:
  - EXTREMELY SIMPLE
  - Focus on teaching concepts, not production-ready code
```

## Feature Progression (Linear Flow)

### Phase 0: Monolith Cache (Baseline)
**What it shows:**
- Single-node, in-memory cache
- GET/PUT operations work fine
- **Problem to highlight:** Single point of failure

**Demo:**
1. Show successful GET/PUT
2. Kill the node → data loss
3. "We need to add another node for reliability!"

### Phase 1: Add Second Node (Broken State) 🎯 **FIRST MVP**
**Feature flag:** `second_node.enabled=true`

**What it shows:**
- Two nodes running independently
- Data NOT synchronized
- Demonstrate the problem:
  - Write to Node 1: `PUT /users/1 = "Alice"`
  - Read from Node 2: `GET /users/1 = 404 Not Found`
  - **This is broken by design!**

**Teaching moment:**
- "See? Just adding nodes doesn't solve the problem!"
- "We need replication to keep them in sync"

### Phase 2: Add Replication (Fixed State)
**Feature flag:** `replication.enabled=true`

**What it shows:**
- Writes to Node 1 are replicated to Node 2
- Both nodes serve the same data
- Leader-follower pattern

**Demo:**
1. Toggle replication ON
2. Write to Node 1: `PUT /users/1 = "Alice"`
3. Read from Node 2: `GET /users/1 = "Alice"` ✅
4. Show dashboard: replication lag, sync status

### Phase 3: Add Consensus (Raft)
**Feature flag:** `consensus.algorithm=raft`

**What it shows:**
- Leader election when node fails
- Prevents split-brain scenarios
- Log-based replication

**Demo:**
1. Toggle consensus ON
2. Kill the leader node
3. Automatic failover to follower (becomes new leader)
4. Show dashboard: leader election process

### Phase 4: Add Sharding
**Feature flag:** `sharding.strategy=consistent_hash`

**What it shows:**
- Data partitioned across nodes
- Each node owns subset of keys
- Hash function determines key location

**Demo:**
1. Toggle sharding ON
2. Write 100 keys
3. Show dashboard: key distribution across nodes
4. Add third node → rebalancing

### Phase 5: Add Multi-Tenancy
**Feature flag:** `multitenancy.enabled=true`

**What it shows:**
- Namespace isolation per tenant
- Tenant-specific quotas
- Data isolation

**Demo:**
1. Toggle multi-tenancy ON
2. Create tenants: `tenant-a`, `tenant-b`
3. Write keys with tenant context
4. Show dashboard: tenant resource usage

## Demo Flow (2-3 Hour Class)

```
00:00-00:10  Introduction to distributed systems challenges
00:10-00:20  Demo Phase 0: Monolith (show failure)
00:20-00:35  Demo Phase 1: Add second node (show it's broken!)
00:35-00:50  Demo Phase 2: Add replication (now it works!)
00:50-01:10  Demo Phase 3: Add consensus (automatic failover)
01:10-01:30  Demo Phase 4: Add sharding (scalability)
01:30-01:45  Demo Phase 5: Add multi-tenancy (isolation)
01:45-02:00  Chaos engineering: kill nodes, show self-healing
02:00-02:15  Discussion: CAP theorem, tradeoffs
02:15-02:30  Q&A + Quiz
```

## Backup Strategy
- **Pre-record each phase** as video
- If live demo fails → playback mode
- Have screenshots for each state

## MVP Goal (Week 7 Prep)
Build **Phase 0 + Phase 1**:
1. Monolith cache working
2. Add second node (not synced)
3. Dashboard shows both nodes
4. Feature toggle API works
5. Demonstrate the "broken" state

**Success criteria:**
- Can toggle `second_node.enabled=true/false`
- Dashboard updates in real-time
- Students see why "just adding nodes" doesn't work

## Implementation Priorities

### Week 1 (MVP):
- [ ] FastAPI KV store (single node)
- [ ] Docker Compose with 2 nodes
- [ ] Feature flags service
- [ ] Simple dashboard (HTML + WebSocket)
- [ ] Toggle second node on/off
- [ ] Demo the broken state

### Week 2:
- [ ] Add replication logic
- [ ] Update dashboard to show replication
- [ ] Test Phase 0 → Phase 1 → Phase 2 flow

### Week 3+:
- [ ] Add consensus (Raft)
- [ ] Add sharding
- [ ] Add multi-tenancy
- [ ] Polish dashboard
- [ ] Record backup videos

## Key Design Decisions

### 1. Feature Flags Implementation
```python
# config.py
class FeatureFlags:
    second_node_enabled: bool = False
    replication_enabled: bool = False
    consensus_algorithm: str = "none"
    sharding_strategy: str = "none"
    multitenancy_enabled: bool = False

# Toggle API
@app.post("/api/features/{feature_name}")
async def toggle_feature(feature_name: str, enabled: bool):
    setattr(FeatureFlags, feature_name, enabled)
    await notify_all_nodes()
    return {"status": "ok"}
```

### 2. Node Discovery
- Hardcoded node list in docker-compose (simple!)
- Node 1 always starts
- Node 2+ start only if feature flag enabled

### 3. Dashboard State
- WebSocket connection to control plane
- Real-time updates when features toggle
- Show: node count, data distribution, replication status

## Non-Goals (Keep Simple!)
- ❌ Production-ready code
- ❌ Testing all feature combinations
- ❌ Complex UI/UX
- ❌ Performance optimization
- ❌ Security/authentication
- ❌ Persistent storage (in-memory only initially)

## Success Metrics
1. **Teaching effectiveness**: Students understand why each feature is needed
2. **Demo reliability**: Can run Phase 0 → Phase 1 → Phase 2 without crashes
3. **Visual clarity**: Dashboard makes cluster state obvious
4. **Engagement**: Students excited to see features toggle on/off

## References
- Week 1 TaskFlow demo (session management pattern)
- Week 6 CQRS (KV store can back read models)
- Real-world systems: Redis Cluster, DynamoDB, Cassandra

---

**Last Updated:** 2025-10-03
**Status:** Planning → Ready to implement MVP (Phase 0 + Phase 1)
