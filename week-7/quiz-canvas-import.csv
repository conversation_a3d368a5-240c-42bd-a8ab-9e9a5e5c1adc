Type,Unused,Points,Question,Correct Answer,Option 1,Option 2,Option 3,Option 4
MC,,2,"In a CQRS (Command Query Responsibility Segregation) architecture, what is the primary benefit of separating read and write operations?",2,"It reduces the total number of database connections needed","It allows independent optimization of read and write models for their specific use cases","It automatically provides strong consistency across all operations","It eliminates the need for caching mechanisms"
MC,,2,"A financial trading platform needs to detect potentially fraudulent transactions and alert compliance officers within 100 milliseconds of a transaction occurring. Which processing approach is most appropriate?",2,"Batch processing with hourly job execution","Stream processing with real-time event handling","Batch processing with daily aggregation reports","Manual review of all transactions at end of day"
MC,,2,"In a distributed key-value store with 5 nodes, what is the minimum number of nodes that must agree on a value to achieve consensus using a majority-based algorithm like Raft?",2,"2 nodes","3 nodes","4 nodes","5 nodes (all nodes)"
MC,,2,"In an e-commerce system using CQRS with event sourcing, when a customer places an order, which sequence of events would be most appropriate?",2,"Update inventory → Process payment → Create order record → Send confirmation email","Create OrderPlaced event → Update read models asynchronously → Process downstream events","Directly update all databases simultaneously → Send notifications","Create order in database → Manually trigger all related updates"
MC,,2,"According to the CAP theorem, in the event of a network partition, a distributed system must choose between which two properties?",1,"Consistency and Availability","Consistency and Partition Tolerance","Availability and Partition Tolerance","Performance and Scalability"
