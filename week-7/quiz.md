# Week 6-7 Quiz: CQRS, Stream Processing & Distributed Systems

## Instructions
- Answer all questions
- Each question has exactly one correct answer
- Total: 5 multiple choice questions
- Time limit: 15 minutes

---

## Questions

### Question 1: CQRS Pattern Fundamentals

In a CQRS (Command Query Responsibility Segregation) architecture, what is the primary benefit of separating read and write operations?

**Options:**
- A. It reduces the total number of database connections needed
- B. It allows independent optimization of read and write models for their specific use cases
- C. It automatically provides strong consistency across all operations
- D. It eliminates the need for caching mechanisms

<details>
<summary>Answer</summary>

**Correct Answer:** B

**Explanation:**
The primary benefit of CQRS is that it allows independent optimization of read and write models. Write models can be optimized for consistency and business rules (normalized, ACID transactions), while read models can be optimized for query performance (denormalized, specialized indexes). This separation enables better performance and scalability for both operations.

</details>

---

### Question 2: Stream Processing vs Batch Processing

A financial trading platform needs to detect potentially fraudulent transactions and alert compliance officers within 100 milliseconds of a transaction occurring. Which processing approach is most appropriate?

**Options:**
- A. Batch processing with hourly job execution
- B. Stream processing with real-time event handling
- C. Batch processing with daily aggregation reports
- D. Manual review of all transactions at end of day

<details>
<summary>Answer</summary>

**Correct Answer:** B

**Explanation:**
Stream processing is essential for this use case because it processes events as they arrive, enabling sub-second latency. The 100ms requirement cannot be met with batch processing, which typically operates on collected data at scheduled intervals (hours or days). Real-time fraud detection requires immediate analysis of each transaction as it occurs.

</details>

---

### Question 3: Distributed Systems Consensus

In a distributed key-value store with 5 nodes, what is the minimum number of nodes that must agree on a value to achieve consensus using a majority-based algorithm like Raft?

**Options:**
- A. 2 nodes
- B. 3 nodes
- C. 4 nodes
- D. 5 nodes (all nodes)

<details>
<summary>Answer</summary>

**Correct Answer:** B

**Explanation:**
In a 5-node system, a majority requires at least 3 nodes (more than half of 5). This ensures that even if 2 nodes fail or become unreachable, the remaining 3 nodes can still form a majority and continue operating. This is a fundamental principle in consensus algorithms like Raft to prevent split-brain scenarios.

</details>

---

### Question 4: Event-Driven Architecture

In an e-commerce system using CQRS with event sourcing, when a customer places an order, which sequence of events would be most appropriate?

**Options:**
- A. Update inventory → Process payment → Create order record → Send confirmation email
- B. Create OrderPlaced event → Update read models asynchronously → Process downstream events
- C. Directly update all databases simultaneously → Send notifications
- D. Create order in database → Manually trigger all related updates

<details>
<summary>Answer</summary>

**Correct Answer:** B

**Explanation:**
In CQRS with event sourcing, the command side generates domain events (like OrderPlaced) which are then processed asynchronously to update various read models and trigger downstream processes. This approach provides better decoupling, scalability, and the ability to replay events for system recovery or new projections.

</details>

---

### Question 5: Distributed Systems Trade-offs

According to the CAP theorem, in the event of a network partition, a distributed system must choose between which two properties?

**Options:**
- A. Consistency and Availability
- B. Consistency and Partition Tolerance
- C. Availability and Partition Tolerance
- D. Performance and Scalability

<details>
<summary>Answer</summary>

**Correct Answer:** A

**Explanation:**
The CAP theorem states that during a network partition (P), a distributed system must choose between Consistency (C) and Availability (A). You can either maintain consistency by rejecting requests that might lead to inconsistent state, or maintain availability by accepting requests even if they might create temporary inconsistencies. Partition tolerance is not optional - it's a reality that must be handled.

</details>

---

## Score Interpretation

- **5/5:** Excellent understanding of distributed systems concepts
- **4/5:** Good grasp of CQRS and stream processing fundamentals
- **3/5:** Review key concepts, especially consensus and CAP theorem
- **<3:** Revisit Week 6-7 materials and hands-on demos
